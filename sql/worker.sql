
-- ----------------------------
-- 1、城市指标信息表
-- ----------------------------
create table wk_city_label (
    clabel_id             bigint(20)      not null auto_increment     comment '指标id',
    code                 varchar(20)     not null             comment '指标编码',
    name                 varchar(30)     not null             comment '指标名称',
    parent_id            bigint(20)     default null             comment '父指标id',
    sort                 int             not null             comment '显示顺序',
    tenant_id            bigint(20)      default null         comment '租户编号',
    status               char(1)         not null                   comment '指标状态（0正常 1停用）',
  del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (clabel_id)

) engine=innodb comment = '指标信息表';

-- ----------------------------
-- 2、 用户分组
-- ----------------------------
create table wk_user_group (
  user_group_id        bigint(20)      not null auto_increment    comment '分组id',
  name                 varchar(30)     not null                   comment '分组名称',
  member_user_ids      varchar(1024)   default null               comment '成员编号数组',
  tenant_id            bigint(20)      default null               comment '租户编号',
  status               char(1)         not null                   comment '指标状态（0正常 1停用）',
  del_flag             char(1)         default '0'                comment '删除标志（0代表存在 1代表删除）',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (user_group_id)
) engine=innodb comment = '用户分组表';



-- ----------------------------
-- 3、网格信息表
-- ----------------------------
create table wk_grid (
    grid_id             bigint(20)      not null auto_increment     comment '网格id',
    code                 varchar(20)     not null                   comment '网格编码',
    name                 varchar(30)     not null                   comment '网格名称',
    parent_id            bigint(20)     default null                comment '父网格id',
    sort                 int             not null                   comment '显示顺序',
    tenant_id            bigint(20)      default null               comment '租户编号',
    status               char(1)         not null                   comment '网格状态（0正常 1停用）',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    remark               varchar(500)    default null               comment '备注',
    primary key (grid_id)
) engine=innodb comment = '网格信息表';


-- ----------------------------
-- 4、指标责任人表
-- ----------------------------
create table wk_responsib (
    responsib_id             bigint(20)      not null auto_increment    comment '指标责任id',
    city_label_id            bigint(20)      not null                   comment '指标id',
    city_grid_id             bigint(20)      not null                   comment '网格id',
    responser_id             bigint(20)      not null                   comment '责任人组id',
    sort                     int             default 0               comment '显示顺序',
    tenant_id                bigint(20)      default null               comment '租户编号',
    status                   char(1)         not null                   comment '状态（0正常 1停用）',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    remark                   varchar(500)    default null               comment '备注',
    close_time                int             not null                   comment '结案时间（分钟）',
    primary key (responsib_id)
) engine=innodb comment = '指标责任人表';

-- ----------------------------
-- 5、问题表
-- ----------------------------
create table wk_problems (
    problem_id               bigint(20)      not null auto_increment    comment '问题id',
    serial                   varchar(20)      default null              comment '问题单号',
    user_id                  bigint(20)      not null                   comment '发现问题用户编号',
    city_indexs              text            not null                   comment '指标集包含备注json',
    city_grid                bigint(20)      not null                   comment '网格编号',
    lon                      double          default null               comment '经度',
    lat                      double          default null               comment '纬度',
    address                  varchar(200)    default null               comment '地址',
    picture_url              varchar(200)    default null               comment '图片',
    video_url                varchar(200)    default null               comment '录音',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    remark                   varchar(500)    default null               comment '备注',
    primary key (problem_id)
) engine=innodb comment = '问题表';

-- ----------------------------
-- 6、工作轨迹主表
-- ----------------------------
create table wk_work_master (
    work_id                  bigint(20)      not null auto_increment    comment '工作轨迹主表主键',
    user_id                  bigint(20)      not null                   comment '用户编号',
    start_time               datetime                                   comment '开始工作时间',
    start_lon                double          not null                   comment '开始经度',
    start_lat                double          not null                   comment '开始纬度',
    start_address            varchar(200)    default null               comment '开始地址',
    end_time                 datetime                                   comment '结束工作时间',
    end_lon                  double          not null                   comment '结束经度',
    end_lat                  double          default null               comment '结束纬度',
    end_address              varchar(200)    default null               comment '结束地址',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)    default null                 comment '租户id',
    primary key (work_id)
) engine=innodb comment = '工作轨迹主表';

-- ----------------------------
-- 6-1、当前工作轨迹
-- ----------------------------
create table wk_work_current (
    current_work_id                  bigint(20)      not null auto_increment    comment '工作轨迹主表主键',
    user_id                  bigint(20)      not null                   comment '用户编号',
    lon                double          not null                   comment '经度',
    lat                double          not null                   comment '纬度',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)    default null                 comment '租户id',
    primary key (current_work_id)
) engine=innodb comment = '当前工作轨迹表';


-- ----------------------------
-- 7、工作轨迹副表
-- ----------------------------
create table wk_work_detail (
    work_detail_id           bigint(20)      not null auto_increment    comment '工作轨迹副表',
    pid                      bigint(20)      not null                   comment '工作轨迹主表主键',
    lon                      double          not null                   comment '经度',
    lat                      double          not null                   comment '纬度',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)    default null                 comment '租户id',
    primary key (work_detail_id)
) engine=innodb comment = '工作轨迹副表';

-- ----------------------------
-- 8、上传文件表
-- ----------------------------
create table wk_file_info (
    file_id                  bigint(20)      not null auto_increment    comment '文件id',
    file_name                varchar(255)    default null               comment '文件名',
    full_url                 varchar(255)    default NULL               comment '全文件路径',
    url                      varchar(255)    default NULL               comment '相对文件路径',
    type                     varchar(30)     default NULL               comment '文件类型',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (file_id)
) engine=innodb comment = '上传文件表';

-- ----------------------------
-- 8、上传文件表
-- ----------------------------
create table wk_push_his (
    push_id                  bigint(20)      not null auto_increment    comment '记录id',
    project_id                bigint(20)    default null               comment '项目id',
    data_no                bigint(20)    default null               comment '业务编号',
    type                int    default 0               comment '类型',
    sect_id                     varchar(30)     default NULL               comment '小区id',
    sect_name                 varchar(255)    default NULL               comment '小区名称',
    street_id                      varchar(30)    default NULL               comment '街镇id',
    street_name                     varchar(255)     default NULL               comment '街镇名称',
    csp_id                     varchar(30)     default NULL               comment '物业公司id',
    csp_name                     varchar(255)     default NULL               comment '物业公司名称',
    one_level                     varchar(255)     default NULL               comment '一级指标',
    two_level                     varchar(255)     default NULL               comment '二级指标',
    three_level                     varchar(255)     default NULL               comment '三级指标',
    check_address                     varchar(255)     default NULL               comment '检查地址',
    accessory                     varchar(500)     default NULL               comment '附件图片',
    check_date                     varchar(30)     default NULL               comment '时间维度',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    primary key (push_id)
) engine=innodb comment = '推送的数据历史记录';

-- ----------------------------
-- 9、地址网格关系表
-- ----------------------------
create table wk_addr_grid (
    addr_id                  bigint(20)      not null auto_increment    comment '地址id',
    grid_id                  bigint(20)     default 0                   comment '网格编号',
    address                  varchar(255)    default NULL               comment '地址',
    remark                   varchar(255)    default NULL               comment '备注',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (addr_id)
) engine=innodb comment = '地址网格关系表';

-- ----------------------------
-- 9、地址小区关系表
-- ----------------------------
create table wk_addr_community (
    addr_community_id                  bigint(20)      not null auto_increment    comment '地址id',
    community_id                  bigint(20)     default 0                   comment '小区编号',
    address                  varchar(255)    default NULL               comment '地址',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (addr_community_id)
) engine=innodb comment = '地址小区关系表';

create table wk_area (
    area_id              bigint(20)      not null auto_increment     comment '区域id',
    code                 varchar(20)     not null                   comment '区域编码',
    name                 varchar(30)     not null                   comment '区域名称',
    parent_id            bigint(20)     default null                comment '父区域id',
    manager_id            bigint(20)     default null                comment '负责人用户id',
    sort                 int             not null                   comment '显示顺序',
    tenant_id            bigint(20)      default null               comment '租户编号',
    status               char(1)         not null                   comment '网格状态（0正常 1停用）',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    remark               varchar(500)    default null               comment '备注',
    primary key (area_id)
) engine=innodb comment = '区域信息表';

-- ----------------------------
-- 10、工单表
-- ----------------------------
create table wk_work_order (
    work_order_id               bigint(20)      not null auto_increment    comment '工单id',
    problem_id                   bigint(20)      default null              comment '问题单号',
     serial                   varchar(20)      default null              comment '问题单号',
    user_id                  bigint(20)      not null                   comment '发现问题用户编号',
    city_index               bigint(20)            not null              comment '指标id',
    city_grid                bigint(20)      not null                   comment '网格编号',
    lon                      double          default null               comment '经度',
    lat                      double          default null               comment '纬度',
    address                  varchar(200)    default null               comment '地址',
    picture_url              varchar(200)    default null               comment '图片',
    process_url              varchar(200)    default null               comment '图片',
     status                  int   default 0                            comment '状态',
    video_url                varchar(200)    default null               comment '录音',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    remark                   varchar(500)    default null               comment '备注',
    primary key (work_order_id)
) engine=innodb comment = '工单表';

-- ----------------------------
-- 11、工单处理关联信息
-- ----------------------------
create table wk_work_order_detail (
    order_detail_id          bigint(20)      not null auto_increment    comment '工单详细id',
    work_order_id            bigint(20)      default 0                  comment '关联工单id',
    user_id                  bigint(20)      default null               comment '用户id',
    status                   int             default null               comment '状态',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (order_detail_id)
) engine=innodb comment = '工单处理关联信息';

-- ----------------------------
-- 11、工单处理推送消息
-- ----------------------------
create table wk_order_msg (
    order_msg_id          bigint(20)      not null auto_increment    comment '工单消息id',
    work_order_id            bigint(20)      default 0                  comment '关联工单id',
    user_id                  bigint(20)      default null               comment '用户id',
    device_id                varchar(64)     default null               comment '设备id',
    ext                      varchar(256)     default null               comment '扩展字段',
    msg                      text            default null               comment '消息内容',
    status                   int             default null               comment '状态',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (order_msg_id)
) engine=innodb comment = '工单处理推送消息';

-- ----------------------------
-- 11、用户日表
-- ----------------------------
create table wk_user_day (
    user_day_id          bigint(20)      not null auto_increment    comment '日表id',
    user_id                  bigint(20)      default null               comment '用户id',
    search_date              varchar(8)     default null                comment '日期yyyyMMdd',
    trips_num                varchar(100)     default null               comment '公里数',
    pass_num                bigint(20)     default null               comment '合格数',
    fail_num                bigint(20)     default null               comment '不合格数',
    good_num                bigint(20)     default null               comment '优秀数量',
    ext                      varchar(100)     default null               comment '扩展字段',
    type                     int             default null               comment '类型 0：轨迹 1：评分',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (user_day_id),
    index search_index(search_date)
) engine=innodb comment = '用户日表';

-- ----------------------------
-- 11、用户周表
-- ----------------------------
create table wk_user_week (
    user_week_id          bigint(20)      not null auto_increment    comment '周表id',
    user_id                  bigint(20)      default null               comment '用户id',
    search_date              varchar(12)     default null                comment '日期yyyyMMdd',
    trips_num                varchar(100)     default null               comment '公里数',
    pass_num                 bigint(20)     default null               comment '合格数',
    fail_num                 bigint(20)     default null               comment '不合格数',
    good_num                 bigint(20)     default null               comment '优秀数量',
    ext                      varchar(100)     default null               comment '扩展字段',
    type                     int             default null               comment '类型 0：轨迹 1：评分',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (user_week_id),
    index search_index(search_date)
) engine=innodb comment = '用户周表';

-- ----------------------------
-- 11、用户月表
-- ----------------------------
create table wk_user_month (
    user_month_id          bigint(20)      not null auto_increment    comment '月表id',
    user_id                  bigint(20)      default null               comment '用户id',
    search_date              varchar(6)     default null                comment '日期yyyyMM',
    trips_num                varchar(100)     default null               comment '公里数',
    pass_num                bigint(20)     default null               comment '合格数',
    fail_num                bigint(20)     default null               comment '不合格数',
    good_num                bigint(20)     default null               comment '优秀数量',
    ext                      varchar(100)     default null               comment '扩展字段',
    type                     int             default null               comment '类型 0：轨迹 1：评分',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (user_month_id),
    index search_index(search_date)
) engine=innodb comment = '用户月表';

-- ----------------------------
-- 12、审核记录表
-- ----------------------------
create table wk_review_record (
    review_record_id          bigint(20)      not null auto_increment    comment '记录id',
    user_id                  bigint(20)      default null               comment '审核员id',
    work_order_id                  bigint(20)      default null               comment '工单id',
    duration                  int      default 0               comment '浏览时间(秒)',
    type                     int             default null               comment '类型 0：页面 1：图片 2:录音',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (review_record_id),
    index search_index(work_order_id)
) engine=innodb comment = '审核记录表';

create table wk_upload_file (
    file_id          bigint(20)      not null auto_increment    comment '文件id',
    user_id                  bigint(20)      default null               comment '审核员id',
    type                     int             default null               comment '类型 0：页面 1：图片 2:录音',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    day                varchar(10)     default ''                 comment '日期',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (file_id)
) engine=innodb comment = '上传大文件表';


create table wk_metric_group (
    metric_group_id          bigint(20)      not null auto_increment    comment '指标组id',
    metric_group_code        varchar (255)   default ''                 comment '指标组编号',
    metric_group_name        varchar (255)   default ''                 comment '指标组名称',
    remark                   varchar (255)   default ''                 comment '备注',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
     `creator`  bigint(20) default null comment "创建者用户id",
    primary key (metric_group_id)
) engine=innodb comment = '指标组';

create table wk_metric_and_group (
    metric_and_group_id          bigint(20)      not null auto_increment    comment '指标组id',
    metric_group_id          bigint(20)      not null     comment '指标组id',
    metric_id          bigint(20)      not null     comment '指标id',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
    primary key (metric_and_group_id)
) engine=innodb comment = '指标组关联表';

create table wk_metric (
    metric_id          bigint(20)      not null auto_increment    comment '指标id',
    metric_code          varchar(255)      not null     comment '指标编号',
    parent_code          varchar(255)      not null     comment '上级指标编号',
    name          varchar(255)      not null     comment '名称',
    description          varchar(255)      not null     comment '描述',
    metric_group_id          bigint(20)      default null     comment '指标组id',
    parent          bigint(20)      default null     comment '上级指标id',
    weight          double(12,2) default 0            comment '权重',
    score          double(12,2) default 0            comment '分数',
    remark                   varchar (255)   default ''                 comment '备注',
    status                 char(1)         default '0'                comment '状态（0代表正常 1代表不正常）',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
     `creator`  bigint(20) default null comment "创建者用户id",
    primary key (metric_id)
) engine=innodb comment = '指标';

create table wk_location_group (
    location_group_id          bigint(20)      not null auto_increment    comment '位置组id',
    location_group_code        varchar (255)   default ''                 comment '位置组编号',
    location_group_name        varchar (255)   default ''                 comment '位置组名称',
    remark                   varchar (255)   default ''                 comment '备注',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
     `creator`  bigint(20) default null comment "创建者用户id",
    primary key (location_group_id)
) engine=innodb comment = '位置组';

create table wk_location (
    location_id          bigint(20)      not null auto_increment    comment '位置组id',
    location_group_id    bigint(20)          comment '位置组id',
    region              varchar (255)   default ''                 comment '区域',
    location_code              varchar (255)   default ''                 comment '小区编号(调研编号)',
    location_name              varchar (255)   default ''                 comment '小区名称',
    address              varchar (255)   default ''                 comment '起点地址',
    start_lon              double (12,6)   default 0                comment '起点经度',
    start_lat              double (12,6)   default 0                 comment '起点纬度',
    end_lon              double (12,6)   default 0                comment '终点经度',
    end_lat              double (12,6)   default 0                 comment '终点纬度',
    location_end              varchar (255)   default ''                 comment '终点地址',
    default_address        varchar (255)   default ''                 comment '默认问题点位置',
    remark                   varchar (255)   default ''                 comment '备注',
    province                   varchar (255)   default ''                 comment '省',
    city                   varchar (255)   default ''                 comment '市',
    district                   varchar (255)   default ''                 comment '区',
    street                   varchar (255)   default ''                 comment '街道',
    loads                   varchar (255)   default ''                 comment '路',
    town                   varchar (255)   default ''                 comment '弄',
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
     `creator`  bigint(20) default null comment "创建者用户id",
    primary key (location_id)
) engine=innodb comment = '位置';

CREATE TABLE IF NOT EXISTS `wk_app_template` (
  `app_template_id` bigint(20) NOT NULL auto_increment,
  `app_template_code` varchar(255) default null comment "模板编号",
  `app_template_name` varchar(255) default null comment "模板名称",
  `is_auto_audio` int(1) default 0 comment "自动录音",
  `is_fill_price` int(1) default 0 comment "扫码填写价格",
  `is_manual_position` int(1) default 0 comment "手动输入地址",
  `is_support_audio` int(1) default 0 comment "支持录音",
  `is_support_picture` int(1) default 0 comment "拍照",
  `is_support_scan` int(1) default 0 comment "扫码",
  `is_support_video` int(1) default 0 comment "视频",
  `allow_edit_position` int(1) default 0 comment "允许修改地址",
  `remark` varchar(255) default null comment "描述备注",
  `sort` int(3) default 0,
  `status` int(1) default 0 comment "状态（0正常 1停用）",
   `creator`  bigint(20) default null comment "创建者用户id",
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
  PRIMARY KEY  (`app_template_id`)
) ENGINE=innodb  comment = 'app模板';

CREATE TABLE IF NOT EXISTS `wk_app_template_field` (
  `app_template_field_id` bigint(20) NOT NULL auto_increment,
  `field_name` varchar(255) default null comment "名称",
  `question_type` int(1) default 1 comment "问卷类型",
  `ref_values` varchar(255) default null comment "问卷内容，已逗号隔开",
  `remark` varchar(255) default null comment "描述备注",
  `app_template_id` bigint(20) NOT NULL comment "关联模板id",
  `sort` int(3) default 0,
  `status` int(1) default 0 comment "状态（0正常 1停用）",
  `is_required` int(1) default 0 comment "是否必须的，0-否 1-是",
   `creator`  bigint(20) default null comment "创建者用户id",
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
  PRIMARY KEY  (`app_template_field_id`)
) ENGINE=innodb  comment = '模板详情';

CREATE TABLE IF NOT EXISTS `wk_project` (
  `project_id` bigint(20) NOT NULL auto_increment,
  `code` varchar(128) default NULL comment "项目代码",
  `name` varchar(128) default NULL comment "项目名称",
  `scope` varchar(128) default NULL comment "区域",
  `user_group_id` bigint(20) default NULL comment "用户分组id",
  `project_temp_id` int default NULL comment "项目模板ID",
  `cal_score_id` int default NULL comment "算分模板ID",
  `description` varchar(128) default NULL comment "项目描述",
  `isAutoAudit1Passed` int(1) default 0 comment "是否一审自动通过，0-否 1-是",
  `autoAudit1PassedTime` int default 0 comment "一审自动通过时间，单位：分钟",
  `isAutoAssign` int(1) default 0 comment "是否自动安排，0-否 1-是",
  `transFlag` int(1) default 0 comment "子任务是否可以转派 0 允许, 1 不允许",
  `projectTempId` bigint default -1 comment "项目模板",
  `status` int(1) default 0 comment "状态，0-未开始，1-进行中，2-已完成",
  `startDate` timestamp  comment "生效开始日期，格式YYYYMMDD，例：20200101",
  `endDate` timestamp  comment "生效结束日期，格式YYYYMMDD，例：20200101",
  `warningDate` timestamp comment "预警日期，格式YYYYMMDD，例：20200101",
   `metric_group_id` bigint(20) default NULL comment "指标组id",
   location_group_id    bigint(20)  default NULL comment "位置组id",
    `creator`  bigint(20) default null comment "创建者用户id",
    del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
  PRIMARY KEY  (`project_id`)
) ENGINE=innodb  comment = '项目';

CREATE TABLE IF NOT EXISTS `wk_task` (
  `task_id` bigint(20) NOT NULL auto_increment,
  `code` varchar(128) default NULL comment "任务编号",
  `name` varchar(128) default NULL comment "任务名称",
  `area_id` int default NULL comment "街道id",
  `community` int default NULL comment "小区id",
  `project_id` bigint(20) default NULL comment "项目ID",
  `app_id` bigint(20) default null comment "app模板id",
  `surveyc_code` varchar(128) default NULL comment "调研code",
  `survey_scope` varchar(128) default NULL comment "调研区域",
  `survey_target` varchar(128) default NULL comment "调研对象",
  `survey_type` int default 0 comment "调研类型",
  `start_point` varchar(128) default NULL comment "起始位置（经度纬度,分隔）",
  `end_point` varchar(128) default NULL comment "终止位置（经度纬度,分隔）",
  `description` varchar(256) default NULL comment "任务描述",
  `status` int(1) default 0 comment "状态，-1 退回中，0 未开始，1 进行中，2 待审核，3 已完成，4 待提交，5 重做归档",
  `start_date` timestamp  comment "开始时间",
  `end_date` timestamp  comment "结束时间",
  `work_time` bigint default 0 comment "累计工作时长（s）",
  `creator`  bigint(20) default null comment "创建者用户id",
  `principal_id`  bigint(20) default null comment "访问员id",
  `agent_id`  bigint(20) default null comment "代理人id",
  `appointee_id`  bigint(20) default null comment "安排审核人员ID",
  `assign_remark` varchar(256) default NULL comment "安排备注",
  `assignAt` timestamp default CURRENT_TIMESTAMP comment "安排时间",
   address              varchar (255)   default ''                 comment '起点地址',
    start_lon              double (12,6)   default 0                comment '起点经度',
    start_lat              double (12,6)   default 0                 comment '起点纬度',
    end_lon              double (12,6)   default 0                comment '终点经度',
    end_lat              double (12,6)   default 0                 comment '终点纬度',
    location_end              varchar (255)   default ''                 comment '终点地址',
    default_address        varchar (255)   default ''                 comment '默认问题点位置',
     del_flag                 char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by                varchar(64)     default ''                 comment '创建者',
    create_time              datetime                                   comment '创建时间',
    update_by                varchar(64)     default ''                 comment '更新者',
    update_time              datetime                                   comment '更新时间',
    tenant_id                bigint(20)      default null               comment '租户id',
  PRIMARY KEY  (`task_id`)
) ENGINE=innodb  comment = '工单';

-- 4、区域信息表
-- ----------------------------
create table sys_area (
    area_id              bigint(20)      not null auto_increment     comment '区域id',
    code                 varchar(20)     not null                   comment '区域编码',
    name                 varchar(30)     not null                   comment '区域名称',
    parent_id            bigint(20)     default null                comment '父区域id',
    manager_id            bigint(20)     default null                comment '负责人用户id',
    sort                 int             not null                   comment '显示顺序',
    status               char(1)         not null                   comment '网格状态（0正常 1停用）',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    primary key (area_id)
) engine=innodb comment = '区域信息表';

-- 4、审核记录表
-- ----------------------------
create table wk_task_review (
    task_review_id              bigint(20)      not null auto_increment     comment '记录id',
    task_id              bigint(20)      not null     comment '工单id',
    type              int      default 1     comment '类型 1:一审 2：二审 3：复核',
    review_reasion  varchar(256) default NULL comment '原因',
    review_remark  varchar(256) default NULL comment '备注',
    review_status         int         not null                   comment '审核状态',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    primary key (task_review_id)
) engine=innodb comment = '审核记录表';

-- 项目实施人员分配表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `wk_project_implementer` (
  `implementer_id` bigint(20) NOT NULL auto_increment COMMENT '分配记录ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `user_id` bigint(20) NOT NULL COMMENT '实施人员用户ID',
  `execution_order` int(1) DEFAULT 1 COMMENT '执行顺序：1-正序，2-逆序',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `status` int(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`implementer_id`),
  UNIQUE KEY `uk_project_user_order` (`project_id`, `user_id`, `execution_order`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='项目实施人员分配表';

create table wk_task_answer (
    task_answer_id              bigint(20)      not null auto_increment     comment '问卷答案id',
    task_id              bigint(20)      not null     comment '工单id',
    app_template_field_id              bigint(20)      not null     comment '模板字段id',
    answer  varchar(256) default NULL comment '问题内容',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    primary key (task_answer_id)
) engine=innodb comment = '问卷答案表';

create table wk_task_picture (
    task_picture_id              bigint(20)      not null auto_increment     comment '图片id',
    task_id              bigint(20)      not null     comment '工单id',
    url  varchar(256) default NULL comment '问题内容',
    point  varchar(256) default NULL comment '位置',
    type  int default NULL comment '类型 1-门头 2-小区单元 3-营业执照',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    city_index              bigint(20)      not null     comment '指标id',
    address              varchar(255)     default ''      comment '地址',
    location              varchar(255)     default ''      comment '位置',
    lon              double (12,6)     default 0      comment '经度',
    lat              double(12,6)     default 0      comment '纬度',
    primary key (task_picture_id)
) engine=innodb comment = '图片表';

create table wk_task_review_history (
    review_history_id              bigint(20)      not null auto_increment     comment '修改记录id',
    task_id              bigint(20)      not null     comment '工单id',
    url  varchar(256) default NULL comment '问题内容',
    point  varchar(256) default NULL comment '位置',
    type  int default NULL comment '类型 1-门头 2-小区单元 3-营业执照',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    city_index              bigint(20)      not null     comment '指标id',
    address              varchar(255)     default ''      comment '地址',
    location              varchar(255)     default ''      comment '位置',
    lon              double (12,6)     default 0      comment '经度',
    lat              double(12,6)     default 0      comment '纬度',
    operate_type              int     default 0      comment '操作类型 0：修改 1新增 2删除',
    primary key (review_history_id),
    index (task_id)
) engine=innodb comment = '审核修改记录表';

create table wk_task_record (
    task_record_id              bigint(20)      not null auto_increment     comment '录音id',
    task_id              bigint(20)      not null     comment '工单id',
    url  varchar(256) default NULL comment '问题内容',
    point  varchar(256) default NULL comment '位置',
    type  int default NULL comment '类型 0:录音',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    primary key (task_record_id)
) engine=innodb comment = '录音表';

create table wk_community_degree (
    community_degree_id              bigint(20)      not null auto_increment     comment '满意度记录id',
    code              varchar(256)     not null     comment '小区编号',
    property             varchar(256)     default null     comment '物业公司名称',
    area             double(12,4)     default null    comment '面积 万平方',
    price             double(12,4)     default null    comment '房价 万平方',
    degree_unit             double(12,4)     default null    comment '满意度得分',
    effect_unit             double(12,4)     default null    comment '实效得分',
    multiple_unit             double(12,4)     default null    comment '综合得分',
    type             varchar(255)     default null      comment '房屋类型',
    period_date             varchar(256)     default null     comment '统计时间',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    primary key (community_degree_id),
    index (period_date)
) engine=innodb comment = '满意度表';

create table wk_community_complain (
    community_complain_id              bigint(20)      not null auto_increment     comment '投诉记录id',
    code              varchar(256)     not null     comment '小区编号',
    complain_label_id  bigint(20)      not null     comment '投诉指标id',
    count  bigint(20)      default 0     comment '件数',
    period_date             varchar(256)     default null     comment '统计时间',
    del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
    create_by            varchar(64)     default ''                 comment '创建者',
    create_time          datetime                                   comment '创建时间',
    update_by            varchar(64)     default ''                 comment '更新者',
    update_time          datetime                                   comment '更新时间',
    primary key (community_complain_id),
    index (period_date)
) engine=innodb comment = '小区投诉表';

create table wk_complain_label (
    complain_label_id             bigint(20)      not null auto_increment     comment '指标id',
    code                 varchar(20)     not null             comment '指标编码',
    name                 varchar(30)     not null             comment '指标名称',
    parent_id            bigint(20)     default null             comment '父指标id',
    sort                 int             not null             comment '显示顺序',
    tenant_id            bigint(20)      default null         comment '租户编号',
    status               char(1)         not null                   comment '指标状态（0正常 1停用）',
  del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (complain_label_id)

) engine=innodb comment = '投诉指标表';

create table wk_task_history_statics (
    task_history_statics_id             bigint(20)      not null auto_increment     comment '历史记录统计id',
    task_id              bigint(20)      not null                 comment '工单id',
    code                 varchar(20)     not null                 comment '小区编码',
    label_id            bigint(20)     default null               comment '指标id',
    num                 bigint(20)     default 0               comment '指标数量',
    audit1_num            bigint(20)     default 0               comment '一审修改数量',
    audit2_num            bigint(20)     default 0               comment '二审修改数量',
    review_num            bigint(20)     default 0               comment '复核修改数量',
    check_minutes            bigint(20)     default 0               comment '检查时长（分钟）',
  del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  primary key (task_history_statics_id)
) engine=innodb comment = '实效工单历史记录统计表';

commit;