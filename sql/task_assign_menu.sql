-- 工单自动分配菜单配置
-- 注意：需要根据实际的菜单结构调整parent_id
-- 这里假设工单管理的菜单ID需要查询数据库确定

-- 查询工单管理菜单ID的SQL（需要根据实际情况调整）
-- SELECT menu_id FROM sys_menu WHERE menu_name = '工单管理' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '实效管理');

-- 工单自动分配主菜单
-- 假设工单管理的菜单ID是2001，需要根据实际情况调整
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工单自动分配', 2001, 3, 'taskAssign', 'wk/taskAssign/index', 1, 0, 'C', '0', '0', 'wk:taskAssign:list', 'guide', 'admin', sysdate(), '', null, '工单自动分配菜单');

-- 获取刚插入的菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 工单自动分配按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工单分配查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'wk:taskAssign:query', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('工单分配列表', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'wk:taskAssign:list', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('分配给单个用户', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'wk:taskAssign:assignSingle', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('分配给两个用户', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'wk:taskAssign:assignTwo', '#', 'admin', sysdate(), '', null, '');

-- 查询菜单配置结果
SELECT 
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.order_num,
    m1.path,
    m1.component,
    m1.perms,
    m1.menu_type
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_name LIKE '%工单自动分配%' OR m1.perms LIKE '%taskAssign%'
ORDER BY m1.parent_id, m1.order_num;

-- 使用说明：
-- 1. 执行前需要先查询实际的菜单结构，确定正确的parent_id
-- 2. 可以通过以下SQL查询现有菜单结构：
--    SELECT menu_id, menu_name, parent_id FROM sys_menu WHERE menu_name IN ('实效管理', '工单管理') ORDER BY parent_id;
-- 3. 根据查询结果修改上面INSERT语句中的parent_id值（假设是2001）
-- 4. 执行完成后，需要重新登录系统或刷新权限缓存才能看到新菜单
