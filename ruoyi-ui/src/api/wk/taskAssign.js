import request from '@/utils/request'

// 查询项目列表（带未分配工单数量）
export function getProjectsWithTaskCount(query) {
  return request({
    url: '/wk/taskAssign/projects',
    method: 'get',
    params: query
  })
}

// 获取项目下未分配的工单数量
export function getUnassignedTaskCount(projectId) {
  return request({
    url: '/wk/taskAssign/unassignedCount/' + projectId,
    method: 'get'
  })
}

// 获取项目下未分配的工单ID列表
export function getUnassignedTaskIds(projectId) {
  return request({
    url: '/wk/taskAssign/unassignedTasks/' + projectId,
    method: 'get'
  })
}

// 自动分配工单给单个实施人员
export function assignToSingleUser(data) {
  return request({
    url: '/wk/taskAssign/assignToSingle',
    method: 'post',
    data: data
  })
}

// 自动分配工单给两个实施人员（平均分配）
export function assignToTwoUsers(data) {
  return request({
    url: '/wk/taskAssign/assignToTwo',
    method: 'post',
    data: data
  })
}

// 获取用户已分配的项目列表
export function getUserProjects(userId) {
  return request({
    url: '/wk/taskAssign/userProjects/' + userId,
    method: 'get'
  })
}
