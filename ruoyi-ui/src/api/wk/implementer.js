import request from '@/utils/request'

// 查询项目实施人员分配列表
export function listImplementer(query) {
  return request({
    url: '/wk/implementer/list',
    method: 'get',
    params: query
  })
}

// 根据项目ID查询实施人员分配列表
export function listImplementerByProject(projectId) {
  return request({
    url: '/wk/implementer/listByProject/' + projectId,
    method: 'get'
  })
}

// 根据用户ID查询已分配的项目列表
export function listProjectsByUser(userId) {
  return request({
    url: '/wk/implementer/listByUser/' + userId,
    method: 'get'
  })
}

// 查询项目实施人员分配详细
export function getImplementer(implementerId) {
  return request({
    url: '/wk/implementer/' + implementerId,
    method: 'get'
  })
}

// 新增项目实施人员分配
export function addImplementer(data) {
  return request({
    url: '/wk/implementer',
    method: 'post',
    data: data
  })
}

// 为项目分配实施人员
export function assignImplementers(data) {
  return request({
    url: '/wk/implementer/assign',
    method: 'post',
    data: data
  })
}

// 自动分配工单给实施人员
export function autoAssignTasks(projectId) {
  return request({
    url: '/wk/implementer/autoAssignTasks/' + projectId,
    method: 'post'
  })
}

// 检查用户是否已分配到项目
export function checkUserAssigned(projectId, userId) {
  return request({
    url: '/wk/implementer/checkAssigned/' + projectId + '/' + userId,
    method: 'get'
  })
}

// 获取项目的实施人员数量
export function getImplementerCount(projectId) {
  return request({
    url: '/wk/implementer/count/' + projectId,
    method: 'get'
  })
}

// 修改项目实施人员分配
export function updateImplementer(data) {
  return request({
    url: '/wk/implementer',
    method: 'put',
    data: data
  })
}

// 删除项目实施人员分配
export function delImplementer(implementerId) {
  return request({
    url: '/wk/implementer/' + implementerId,
    method: 'delete'
  })
}
