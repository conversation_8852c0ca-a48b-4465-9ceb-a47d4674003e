<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择项目类型" clearable>
          <el-option label="人查" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目开始时间">
        <el-date-picker
          v-model="queryParams.startdate"
          type="date"
          placeholder="选择开始时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
        />
      </el-form-item>
      <el-form-item label="项目结束时间">
        <el-date-picker
          v-model="queryParams.enddate"
          type="date"
          placeholder="选择结束时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目ID" align="center" prop="projectId" width="80" />
      <el-table-column label="项目名称" align="center" prop="name" />
      <el-table-column label="项目类型" align="center" prop="type" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.project_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startdate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startdate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="enddate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.enddate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="未分配工单数" align="center" prop="unassignedTaskCount" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.unassignedTaskCount > 0 ? 'warning' : 'success'">
            {{ scope.row.unassignedTaskCount || 0 }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleAssign(scope.row)"
          >分配实施人员</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 分配实施人员对话框 -->
    <el-dialog :title="assignDialog.title" :visible.sync="assignDialog.open" width="600px" append-to-body>
      <el-form ref="assignForm" :model="assignForm" :rules="assignRules" label-width="120px">
        <el-form-item label="项目名称">
          <el-input v-model="assignForm.projectName" :disabled="true" />
        </el-form-item>
        <el-form-item label="未分配工单数">
          <el-input v-model="assignForm.unassignedTaskCount" :disabled="true" />
        </el-form-item>
        <el-form-item label="分配方式" prop="assignType">
          <el-radio-group v-model="assignForm.assignType" @change="onAssignTypeChange">
            <el-radio :label="1">分配给单个实施人员</el-radio>
            <el-radio :label="2">分配给两个实施人员（平均分配）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="assignForm.assignType === 1" label="实施人员" prop="singleUserId">
          <user-select v-model="assignForm.singleUserId" placeholder="请选择实施人员" />
          <div v-if="singleUserProjects.length > 0" style="margin-top: 5px;">
            <el-tag type="info" size="mini">
              已分配项目: {{ singleUserProjects.map(p => p.projectName).join(', ') }}
            </el-tag>
          </div>
        </el-form-item>
        <template v-if="assignForm.assignType === 2">
          <el-form-item label="正序实施人员" prop="forwardUserId">
            <user-select v-model="assignForm.forwardUserId" placeholder="请选择正序实施人员" />
            <div v-if="forwardUserProjects.length > 0" style="margin-top: 5px;">
              <el-tag type="info" size="mini">
                已分配项目: {{ forwardUserProjects.map(p => p.projectName).join(', ') }}
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item label="逆序实施人员" prop="reverseUserId">
            <user-select v-model="assignForm.reverseUserId" placeholder="请选择逆序实施人员" />
            <div v-if="reverseUserProjects.length > 0" style="margin-top: 5px;">
              <el-tag type="info" size="mini">
                已分配项目: {{ reverseUserProjects.map(p => p.projectName).join(', ') }}
              </el-tag>
            </div>
          </el-form-item>
        </template>
        <el-form-item>
          <el-alert
            title="分配说明"
            type="info"
            :closable="false"
            show-icon>
            <template v-if="assignForm.assignType === 1">
              所有未分配工单将分配给选择的实施人员
            </template>
            <template v-else>
              未分配工单将平均分配给两个实施人员：正序实施人员分配前一半，逆序实施人员分配后一半
            </template>
          </el-alert>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAssign" :loading="assignLoading">确定分配</el-button>
        <el-button @click="cancelAssign">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProjectsWithTaskCount,
  assignToSingleUser,
  assignToTwoUsers,
  getUserProjects
} from "@/api/wk/taskAssign";
import userSelect from '@/components/userSelect/index'

export default {
  name: "TaskAssign",
  dicts: ['sys_normal_disable', 'project_type'],
  components: { userSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目表格数据
      projectList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        type: 1, // 默认查询人查项目
        status: '0', // 默认查询正常状态
        startdate: null,
        enddate: null
      },
      // 分配对话框
      assignDialog: {
        open: false,
        title: "分配实施人员"
      },
      // 分配表单
      assignForm: {
        projectId: null,
        projectName: '',
        unassignedTaskCount: 0,
        assignType: 1, // 1-单个实施人员，2-两个实施人员
        singleUserId: null,
        forwardUserId: null,
        reverseUserId: null
      },
      // 分配表单校验
      assignRules: {
        assignType: [
          { required: true, message: "请选择分配方式", trigger: "change" }
        ],
        singleUserId: [
          { required: true, message: "请选择实施人员", trigger: "change" }
        ],
        forwardUserId: [
          { required: true, message: "请选择正序实施人员", trigger: "change" }
        ],
        reverseUserId: [
          { required: true, message: "请选择逆序实施人员", trigger: "change" }
        ]
      },
      // 分配加载状态
      assignLoading: false,
      // 用户已分配的项目
      singleUserProjects: [],
      forwardUserProjects: [],
      reverseUserProjects: []
    };
  },
  created() {
    this.getList();
  },
  watch: {
    'assignForm.singleUserId'(newVal) {
      if (newVal) {
        this.getUserProjects(newVal, 'single');
      } else {
        this.singleUserProjects = [];
      }
    },
    'assignForm.forwardUserId'(newVal) {
      if (newVal) {
        this.getUserProjects(newVal, 'forward');
      } else {
        this.forwardUserProjects = [];
      }
    },
    'assignForm.reverseUserId'(newVal) {
      if (newVal) {
        this.getUserProjects(newVal, 'reverse');
      } else {
        this.reverseUserProjects = [];
      }
    }
  },
  methods: {
    /** 查询项目列表 */
    getList() {
      this.loading = true;
      getProjectsWithTaskCount(this.queryParams).then(response => {
        // 处理分页数据结构
        if (response.data && response.data.rows) {
          this.projectList = response.data.rows;
          this.total = response.data.total;
        } else {
          this.projectList = response.rows || [];
          this.total = response.total || 0;
        }
        this.loading = false;

        // 处理未分配工单数量（从remark字段获取）
        for (let project of this.projectList) {
          this.$set(project, 'unassignedTaskCount', parseInt(project.remark) || 0);
        }
      }).catch(error => {
        this.loading = false;
        console.error('获取项目列表失败:', error);
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.projectId)
    },
    /** 分配实施人员 */
    handleAssign(row) {
      this.assignForm = {
        projectId: row.projectId,
        projectName: row.name,
        unassignedTaskCount: row.unassignedTaskCount || 0,
        assignType: 1,
        singleUserId: null,
        forwardUserId: null,
        reverseUserId: null
      };

      this.assignDialog.open = true;
      this.assignDialog.title = "分配实施人员 - " + row.name;
    },
    /** 分配方式改变 */
    onAssignTypeChange() {
      // 清空之前的选择
      this.assignForm.singleUserId = null;
      this.assignForm.forwardUserId = null;
      this.assignForm.reverseUserId = null;
      this.singleUserProjects = [];
      this.forwardUserProjects = [];
      this.reverseUserProjects = [];
    },
    /** 获取用户已分配的项目 */
    getUserProjects(userId, type) {
      getUserProjects(userId).then(response => {
        if (type === 'single') {
          this.singleUserProjects = response.data;
        } else if (type === 'forward') {
          this.forwardUserProjects = response.data;
        } else {
          this.reverseUserProjects = response.data;
        }
      }).catch(error => {
        console.error('获取用户项目失败:', error);
      });
    },
    /** 提交分配 */
    submitAssign() {
      this.$refs["assignForm"].validate(valid => {
        if (valid) {
          // 验证两个实施人员不能是同一个用户
          if (this.assignForm.assignType === 2 &&
              this.assignForm.forwardUserId === this.assignForm.reverseUserId) {
            this.$modal.msgError("正序和逆序不能分配给同一个用户");
            return;
          }

          this.assignLoading = true;

          // 调用自动分配工单的逻辑
          this.autoAssignTasks().then(() => {
            this.$modal.msgSuccess("工单分配成功");
            this.assignDialog.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError("工单分配失败：" + error.message);
          }).finally(() => {
            this.assignLoading = false;
          });
        }
      });
    },
    /** 自动分配工单 */
    async autoAssignTasks() {
      if (this.assignForm.assignType === 1) {
        // 单个实施人员：所有工单分配给该用户
        return assignToSingleUser({
          projectId: this.assignForm.projectId,
          userId: this.assignForm.singleUserId
        });
      } else {
        // 两个实施人员：平均分配
        return assignToTwoUsers({
          projectId: this.assignForm.projectId,
          forwardUserId: this.assignForm.forwardUserId,
          reverseUserId: this.assignForm.reverseUserId
        });
      }
    },
    /** 取消分配 */
    cancelAssign() {
      this.assignDialog.open = false;
      this.resetAssignForm();
    },
    /** 重置分配表单 */
    resetAssignForm() {
      this.assignForm = {
        projectId: null,
        projectName: '',
        unassignedTaskCount: 0,
        assignType: 1,
        singleUserId: null,
        forwardUserId: null,
        reverseUserId: null
      };
      this.singleUserProjects = [];
      this.forwardUserProjects = [];
      this.reverseUserProjects = [];
      this.resetForm("assignForm");
    }
  }
};
</script>
