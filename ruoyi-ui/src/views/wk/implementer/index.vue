<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择项目类型" clearable>
          <el-option label="人查" :value="1" />
          <el-option label="车查" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['wk:implementer:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目ID" align="center" prop="projectId" />
      <el-table-column label="项目名称" align="center" prop="name" />
      <el-table-column label="项目类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.project_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="实施人员" align="center" prop="implementers" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.implementers && scope.row.implementers.length > 0">
            <el-tag 
              v-for="impl in scope.row.implementers" 
              :key="impl.implementerId"
              :type="impl.executionOrder === 1 ? 'primary' : 'success'"
              size="mini"
              style="margin: 2px;"
            >
              {{ impl.nickName }}({{ impl.executionOrder === 1 ? '正序' : '逆序' }})
            </el-tag>
          </div>
          <span v-else style="color: #999;">未分配</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleAssign(scope.row)"
            v-hasPermi="['wk:implementer:assign']"
          >分配实施人员</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-operation"
            @click="handleAutoAssign(scope.row)"
            v-hasPermi="['wk:implementer:autoAssign']"
          >自动分配工单</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 分配实施人员对话框 -->
    <el-dialog :title="assignDialog.title" :visible.sync="assignDialog.open" width="500px" append-to-body>
      <el-form ref="assignForm" :model="assignForm" :rules="assignRules" label-width="100px">
        <el-form-item label="项目名称">
          <el-input v-model="assignForm.projectName" :disabled="true" />
        </el-form-item>
        <el-form-item label="正序实施人员" prop="forwardUserId">
          <user-select v-model="assignForm.forwardUserId" placeholder="请选择正序实施人员" />
          <div v-if="forwardUserProjects.length > 0" style="margin-top: 5px;">
            <el-tag type="warning" size="mini">
              已分配项目: {{ forwardUserProjects.map(p => p.projectName).join(', ') }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="逆序实施人员" prop="reverseUserId">
          <user-select v-model="assignForm.reverseUserId" placeholder="请选择逆序实施人员" />
          <div v-if="reverseUserProjects.length > 0" style="margin-top: 5px;">
            <el-tag type="warning" size="mini">
              已分配项目: {{ reverseUserProjects.map(p => p.projectName).join(', ') }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="自动分配工单">
          <el-switch v-model="assignForm.autoAssignTasks" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAssign">确 定</el-button>
        <el-button @click="cancelAssign">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProject } from "@/api/wk/project";
import { 
  listImplementerByProject, 
  assignImplementers, 
  autoAssignTasks,
  listProjectsByUser 
} from "@/api/wk/implementer";
import userSelect from '@/components/userSelect/index'

export default {
  name: "ProjectImplementer",
  dicts: ['sys_normal_disable', 'project_type'],
  components: { userSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目表格数据
      projectList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        type: 1, // 默认查询人查项目
        status: '0' // 默认查询正常状态
      },
      // 分配对话框
      assignDialog: {
        open: false,
        title: "分配实施人员"
      },
      // 分配表单
      assignForm: {
        projectId: null,
        projectName: '',
        forwardUserId: null,
        reverseUserId: null,
        autoAssignTasks: false
      },
      // 分配表单校验
      assignRules: {
        // 至少选择一个实施人员的验证在提交时处理
      },
      // 正序用户已分配的项目
      forwardUserProjects: [],
      // 逆序用户已分配的项目
      reverseUserProjects: []
    };
  },
  created() {
    this.getList();
  },
  watch: {
    'assignForm.forwardUserId'(newVal) {
      if (newVal) {
        this.getUserProjects(newVal, 'forward');
      } else {
        this.forwardUserProjects = [];
      }
    },
    'assignForm.reverseUserId'(newVal) {
      if (newVal) {
        this.getUserProjects(newVal, 'reverse');
      } else {
        this.reverseUserProjects = [];
      }
    }
  },
  methods: {
    /** 查询项目列表 */
    getList() {
      this.loading = true;
      listProject(this.queryParams).then(response => {
        this.projectList = response.rows;
        this.total = response.total;
        this.loading = false;
        
        // 查询每个项目的实施人员
        this.loadImplementers();
      });
    },
    /** 加载项目实施人员信息 */
    async loadImplementers() {
      for (let project of this.projectList) {
        try {
          const response = await listImplementerByProject(project.projectId);
          this.$set(project, 'implementers', response.data);
        } catch (error) {
          console.error('加载项目实施人员失败:', error);
          this.$set(project, 'implementers', []);
        }
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.projectId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/wk/implementer/add');
    },
    /** 分配实施人员 */
    handleAssign(row) {
      this.assignForm = {
        projectId: row.projectId,
        projectName: row.name,
        forwardUserId: null,
        reverseUserId: null,
        autoAssignTasks: false
      };
      
      // 如果已有实施人员，填充到表单
      if (row.implementers && row.implementers.length > 0) {
        row.implementers.forEach(impl => {
          if (impl.executionOrder === 1) {
            this.assignForm.forwardUserId = impl.userId;
          } else if (impl.executionOrder === 2) {
            this.assignForm.reverseUserId = impl.userId;
          }
        });
      }
      
      this.assignDialog.open = true;
      this.assignDialog.title = "分配实施人员 - " + row.name;
    },
    /** 获取用户已分配的项目 */
    getUserProjects(userId, type) {
      listProjectsByUser(userId).then(response => {
        if (type === 'forward') {
          this.forwardUserProjects = response.data;
        } else {
          this.reverseUserProjects = response.data;
        }
      }).catch(error => {
        console.error('获取用户项目失败:', error);
      });
    },
    /** 提交分配 */
    submitAssign() {
      this.$refs["assignForm"].validate(valid => {
        if (valid) {
          // 验证至少选择一个实施人员
          if (!this.assignForm.forwardUserId && !this.assignForm.reverseUserId) {
            this.$modal.msgError("至少需要分配一个实施人员");
            return;
          }
          
          // 验证不能分配给同一个用户
          if (this.assignForm.forwardUserId && this.assignForm.reverseUserId 
              && this.assignForm.forwardUserId === this.assignForm.reverseUserId) {
            this.$modal.msgError("正序和逆序不能分配给同一个用户");
            return;
          }
          
          assignImplementers(this.assignForm).then(response => {
            this.$modal.msgSuccess("分配成功");
            this.assignDialog.open = false;
            this.getList();
          });
        }
      });
    },
    /** 取消分配 */
    cancelAssign() {
      this.assignDialog.open = false;
      this.resetAssignForm();
    },
    /** 重置分配表单 */
    resetAssignForm() {
      this.assignForm = {
        projectId: null,
        projectName: '',
        forwardUserId: null,
        reverseUserId: null,
        autoAssignTasks: false
      };
      this.forwardUserProjects = [];
      this.reverseUserProjects = [];
      this.resetForm("assignForm");
    },
    /** 自动分配工单 */
    handleAutoAssign(row) {
      this.$modal.confirm('是否确认自动分配项目"' + row.name + '"的工单？').then(function() {
        return autoAssignTasks(row.projectId);
      }).then((response) => {
        this.$modal.msgSuccess(response.msg);
        this.getList();
      }).catch(() => {});
    }
  }
};
</script>
