<template>
 <treeselect v-model="parentId" :options="areaOptions" :normalizer="normalizer" placeholder="请选择父类目" @change="sendChooseId" style="width: 100%;"/>
</template>

<script>
  import { listCitylabel } from "@/api/wk/citylabel";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

 export default {
   name: "LabelSelect",
   components: {
     Treeselect
   },
   props:{
     value:Number
   },
  data() {
     return {
       parentId:null,
       loading:false,
   // 区域信息表格数据
      areaList: [],
      // 类目信息树选项
      areaOptions: [],
      // 查询参数
      queryParams: {
        code: null,
        name: null,
        parentId: null,
        managerId: null,
        sort: null,
        tenantId: null,
        status: null,
      },
     };
   },
   watch:{
     parentId:function(o1,o2){
        this.$emit('input',o1);
     },
     value:function(o1,o2){
        this.parentId = o1;
     },
   },
  created() {
      this.getTreeselect();
  },
  methods: {
    getList() {
        this.loading = true;
        listCitylabel(this.queryParams).then(response => {
          this.areaList = this.handleTree(response.data, "clabelId", "parentId");
          this.loading = false;
        });
      },
      /** 转换区域信息数据结构 */
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.clabelId,
          label: node.name,
          children: node.children
        };
      },
    /** 查询区域信息下拉树结构 */
      getTreeselect() {
        listCitylabel().then(response => {
          this.areaOptions = [];
          const data = { clabelId: 0, name: '顶级节点', children: [] };
          data.children = this.handleTree(response.data, "clabelId", "parentId");
          this.areaOptions.push(data);
        });
      },
      sendChooseId() {
        console.log(this.parentId)
        this.$emit('sendId',this.parentId);
      }
  },
  };
</script>

<style>
</style>
