<template>
    <el-select v-model="userId" @change="onchange" :placeholder="placeholder" filterable>
        <el-option
            label="取消选择"
            :value="''"
        ></el-option>
        <el-option
            v-for="item in userList"
            :key="item.userId"
            :label="item.nickName"
            :value="item.userId"
        ></el-option>
    </el-select>
</template>

<script>
import { listUsers,wlistUsers } from "@/api/system/user";

export default {
    name:'userSelect',
    data(){
        return {
            userList:[],
            userId: null
        }
    },
    props:{
        value: {
            default: null
        },
        placeholder: {
            default: '请选择'
        },
       type:{
         default:true
       }
    },
    watch:{
        value(){
            this.userId = this.value
        }
    },
    created(){
        this.getList()
        this.userId = this.value
    },
    methods:{
        /** 查询用户列表 */
        getList() {
          if (this.type) {
            listUsers().then(response => {
                console.log(response.rows)
                this.userList = response.rows;
            });
          } else{
            wlistUsers().then(response => {
                console.log(response.rows)
                this.userList = response.rows;
            });
          }
        },
        onchange(e){
            console.log(e)
            this.$emit('input',e)
        }
    }
}
</script>
