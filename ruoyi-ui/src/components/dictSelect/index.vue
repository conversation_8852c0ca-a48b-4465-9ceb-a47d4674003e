<template>
    <el-select v-model="dictId" @change="onchange" :placeholder="placeholder" filterable>
        <el-option
            label="取消选择"
            :value="''"
        ></el-option>
        <el-option
            v-for="item in data"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        ></el-option>
    </el-select>
</template>

<script>

export default {
    name:'dictSelect',
    data(){
        return {
            dictId: null
        }
    },
    props:{
        data: {
            default: []
        },
        value: {
            default: null
        },
        placeholder: {
            default: '请选择'
        },
    },
    watch:{
        value(){
            this.dictId = this.value
        }
    },
    created(){
        this.dictId = this.value
    },
    methods:{
        onchange(e){
            console.log(e)
            this.$emit('input',e)
        }
    }
}
</script>