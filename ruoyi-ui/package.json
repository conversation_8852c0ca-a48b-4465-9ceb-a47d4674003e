{"name": "ruoyi", "version": "3.8.5", "description": "工作流系统", "author": "若依", "license": "MIT", "scripts": {"dev": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve", "build:prod": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build", "build:stage": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "echarts": "5.4.0", "element-ui": "2.15.10", "exifreader": "^4.17.0", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "less-loader": "^6.2.0", "nprogress": "0.2.0", "panolens": "^0.12.1", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "three": "^0.105.2", "vant": "^2.12.47", "vue": "2.6.12", "vue-baidu-map": "^0.21.22", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-mini-player": "^0.2.1", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "swiper": "^3.4.2", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}