# 工单自动分配功能测试指南

## 测试前准备

### 1. 数据准备
确保系统中有以下测试数据：

```sql
-- 检查项目数据
SELECT project_id, name, type, status, startdate, enddate 
FROM wk_project 
WHERE type = 1 AND status = 0 
LIMIT 5;

-- 检查用户数据
SELECT user_id, nick_name, status 
FROM sys_user 
WHERE status = '0' AND del_flag = '0' 
LIMIT 10;

-- 检查未分配工单数据
SELECT task_id, project_id, principal_id, status 
FROM wk_task 
WHERE principal_id IS NULL 
LIMIT 20;
```

### 2. 菜单配置
1. 执行菜单配置SQL：`sql/task_assign_menu.sql`
2. 为测试用户角色分配相应权限
3. 重新登录系统

## 功能测试步骤

### 1. 页面访问测试
- [ ] 登录系统
- [ ] 导航到：实效管理 -> 工单管理 -> 工单自动分配
- [ ] 验证页面正常加载，显示项目列表

### 2. 筛选功能测试
- [ ] **项目名称筛选**：输入项目名称，点击搜索，验证结果正确
- [ ] **项目类型筛选**：选择"人查"，验证只显示人查项目
- [ ] **状态筛选**：选择"正常"，验证只显示正常状态项目
- [ ] **开始时间筛选**：选择开始时间，验证筛选结果
- [ ] **结束时间筛选**：选择结束时间，验证筛选结果
- [ ] **组合筛选**：同时使用多个筛选条件，验证结果正确
- [ ] **重置功能**：点击重置按钮，验证筛选条件清空

### 3. 项目列表显示测试
- [ ] 验证项目基本信息正确显示（ID、名称、类型、状态、时间）
- [ ] 验证未分配工单数量正确显示
- [ ] 验证未分配工单数量的颜色标识（有工单显示橙色，无工单显示绿色）
- [ ] 验证分页功能正常工作

### 4. 单个实施人员分配测试

#### 4.1 正常分配流程
- [ ] 选择有未分配工单的项目，点击"分配实施人员"
- [ ] 选择"分配给单个实施人员"
- [ ] 选择一个实施人员
- [ ] 验证显示该用户已分配的其他项目（如果有）
- [ ] 点击"确定分配"
- [ ] 验证分配成功提示
- [ ] 验证项目的未分配工单数量更新为0

#### 4.2 已分配项目提示测试
- [ ] 选择一个已有工单分配的用户
- [ ] 验证显示"已分配项目"提示信息
- [ ] 验证提示信息中的项目名称正确

### 5. 两个实施人员分配测试

#### 5.1 正常分配流程
- [ ] 选择有未分配工单的项目（建议选择工单数量 >= 10的项目）
- [ ] 选择"分配给两个实施人员（平均分配）"
- [ ] 选择正序实施人员
- [ ] 选择逆序实施人员（不同于正序实施人员）
- [ ] 验证两个用户的已分配项目提示
- [ ] 点击"确定分配"
- [ ] 验证分配成功提示，显示具体分配数量
- [ ] 验证项目的未分配工单数量更新为0

#### 5.2 验证规则测试
- [ ] 尝试选择相同用户作为正序和逆序实施人员
- [ ] 验证显示错误提示："正序和逆序不能分配给同一个用户"

### 6. 异常情况测试

#### 6.1 无未分配工单的项目
- [ ] 选择未分配工单数量为0的项目
- [ ] 尝试分配实施人员
- [ ] 验证显示相应的提示信息

#### 6.2 表单验证测试
- [ ] 不选择分配方式，点击确定，验证验证提示
- [ ] 选择单个实施人员模式但不选择用户，验证提示
- [ ] 选择两个实施人员模式但只选择一个用户，验证提示

### 7. 数据一致性验证

#### 7.1 数据库验证
分配完成后，检查数据库中的数据：

```sql
-- 验证工单的principal_id已更新
SELECT task_id, project_id, principal_id 
FROM wk_task 
WHERE project_id = [测试项目ID] 
ORDER BY task_id;

-- 验证分配的均匀性（两个实施人员模式）
SELECT principal_id, COUNT(*) as task_count 
FROM wk_task 
WHERE project_id = [测试项目ID] AND principal_id IS NOT NULL 
GROUP BY principal_id;
```

#### 7.2 分配算法验证
- [ ] 对于单个实施人员：验证所有工单都分配给了选择的用户
- [ ] 对于两个实施人员：验证工单平均分配，正序用户分配前一半，逆序用户分配后一半
- [ ] 对于奇数工单：验证逆序用户多分配一个工单

### 8. 性能测试
- [ ] 测试大量项目的加载性能（如果有100+项目）
- [ ] 测试大量工单的分配性能（如果有1000+工单）
- [ ] 测试并发分配的情况

### 9. 用户体验测试
- [ ] 验证加载状态显示正确
- [ ] 验证操作反馈及时
- [ ] 验证错误提示友好
- [ ] 验证页面响应式设计

## 预期结果

### 正常功能
1. 所有筛选条件正常工作
2. 未分配工单数量准确显示
3. 工单分配按规则正确执行
4. 已分配项目提示准确显示
5. 数据一致性良好

### 错误处理
1. 输入验证错误有友好提示
2. 业务规则验证正确
3. 异常情况处理得当

## 问题记录表

| 问题描述 | 重现步骤 | 预期结果 | 实际结果 | 严重程度 | 状态 |
|---------|---------|---------|---------|---------|------|
|         |         |         |         |         |      |

## 测试完成确认

- [ ] 所有基础功能测试通过
- [ ] 所有异常情况测试通过
- [ ] 数据一致性验证通过
- [ ] 用户体验测试通过
- [ ] 性能测试通过（如适用）

## 部署检查清单

- [ ] 后端控制器文件已部署
- [ ] 前端页面文件已部署
- [ ] API接口文件已部署
- [ ] 菜单配置已执行
- [ ] 权限配置已完成
- [ ] 测试数据已准备
- [ ] 功能测试已通过
