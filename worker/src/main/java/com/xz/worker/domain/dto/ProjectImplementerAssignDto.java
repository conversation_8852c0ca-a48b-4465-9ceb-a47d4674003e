package com.xz.worker.domain.dto;

import java.util.List;

/**
 * 项目实施人员分配DTO
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public class ProjectImplementerAssignDto {
    
    /** 项目ID */
    private Long projectId;
    
    /** 正序实施人员ID */
    private Long forwardUserId;
    
    /** 逆序实施人员ID */
    private Long reverseUserId;
    
    /** 是否立即分配工单 */
    private Boolean autoAssignTasks;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getForwardUserId() {
        return forwardUserId;
    }

    public void setForwardUserId(Long forwardUserId) {
        this.forwardUserId = forwardUserId;
    }

    public Long getReverseUserId() {
        return reverseUserId;
    }

    public void setReverseUserId(Long reverseUserId) {
        this.reverseUserId = reverseUserId;
    }

    public Boolean getAutoAssignTasks() {
        return autoAssignTasks;
    }

    public void setAutoAssignTasks(Boolean autoAssignTasks) {
        this.autoAssignTasks = autoAssignTasks;
    }

    @Override
    public String toString() {
        return "ProjectImplementerAssignDto{" +
                "projectId=" + projectId +
                ", forwardUserId=" + forwardUserId +
                ", reverseUserId=" + reverseUserId +
                ", autoAssignTasks=" + autoAssignTasks +
                '}';
    }
}
