package com.xz.worker.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目实施人员分配对象 wk_project_implementer
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public class WkProjectImplementer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分配记录ID */
    private Long implementerId;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 实施人员用户ID */
    @Excel(name = "实施人员用户ID")
    private Long userId;

    /** 执行顺序：1-正序，2-逆序 */
    @Excel(name = "执行顺序", readConverterExp = "1=正序,2=逆序")
    private Integer executionOrder;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /** 状态：0-禁用，1-启用 */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private Integer status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 租户id */
    private Long tenantId;

    // 关联查询字段
    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 实施人员姓名 */
    @Excel(name = "实施人员姓名")
    private String userName;

    /** 实施人员昵称 */
    @Excel(name = "实施人员昵称")
    private String nickName;

    public void setImplementerId(Long implementerId) 
    {
        this.implementerId = implementerId;
    }

    public Long getImplementerId() 
    {
        return implementerId;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setExecutionOrder(Integer executionOrder) 
    {
        this.executionOrder = executionOrder;
    }

    public Integer getExecutionOrder() 
    {
        return executionOrder;
    }
    public void setAssignTime(Date assignTime) 
    {
        this.assignTime = assignTime;
    }

    public Date getAssignTime() 
    {
        return assignTime;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setTenantId(Long tenantId) 
    {
        this.tenantId = tenantId;
    }

    public Long getTenantId() 
    {
        return tenantId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("implementerId", getImplementerId())
            .append("projectId", getProjectId())
            .append("userId", getUserId())
            .append("executionOrder", getExecutionOrder())
            .append("assignTime", getAssignTime())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("tenantId", getTenantId())
            .toString();
    }
}
