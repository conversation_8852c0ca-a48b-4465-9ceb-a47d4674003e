package com.xz.worker.mapper;

import java.util.List;
import com.xz.worker.domain.WkProjectImplementer;
import org.apache.ibatis.annotations.Param;

/**
 * 项目实施人员分配Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface WkProjectImplementerMapper 
{
    /**
     * 查询项目实施人员分配
     * 
     * @param implementerId 项目实施人员分配主键
     * @return 项目实施人员分配
     */
    public WkProjectImplementer selectWkProjectImplementerByImplementerId(Long implementerId);

    /**
     * 查询项目实施人员分配列表
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 项目实施人员分配集合
     */
    public List<WkProjectImplementer> selectWkProjectImplementerList(WkProjectImplementer wkProjectImplementer);

    /**
     * 根据项目ID查询实施人员分配列表
     * 
     * @param projectId 项目ID
     * @return 项目实施人员分配集合
     */
    public List<WkProjectImplementer> selectImplementersByProjectId(Long projectId);

    /**
     * 根据用户ID查询已分配的项目列表
     * 
     * @param userId 用户ID
     * @return 项目实施人员分配集合
     */
    public List<WkProjectImplementer> selectProjectsByUserId(Long userId);

    /**
     * 检查用户是否已分配到项目
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 分配记录数量
     */
    public int checkUserAssigned(@Param("projectId") Long projectId, @Param("userId") Long userId);

    /**
     * 新增项目实施人员分配
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 结果
     */
    public int insertWkProjectImplementer(WkProjectImplementer wkProjectImplementer);

    /**
     * 修改项目实施人员分配
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 结果
     */
    public int updateWkProjectImplementer(WkProjectImplementer wkProjectImplementer);

    /**
     * 删除项目实施人员分配
     * 
     * @param implementerId 项目实施人员分配主键
     * @return 结果
     */
    public int deleteWkProjectImplementerByImplementerId(Long implementerId);

    /**
     * 批量删除项目实施人员分配
     * 
     * @param implementerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWkProjectImplementerByImplementerIds(Long[] implementerIds);

    /**
     * 根据项目ID删除实施人员分配
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteImplementersByProjectId(Long projectId);

    /**
     * 批量插入项目实施人员分配
     * 
     * @param implementers 项目实施人员分配列表
     * @return 结果
     */
    public int batchInsertImplementers(List<WkProjectImplementer> implementers);

    /**
     * 统计项目的实施人员数量
     * 
     * @param projectId 项目ID
     * @return 实施人员数量
     */
    public int countImplementersByProjectId(Long projectId);
}
