package com.xz.worker.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.xz.worker.domain.WkProjectImplementer;
import com.xz.worker.domain.dto.ProjectImplementerAssignDto;
import com.xz.worker.service.IWkProjectImplementerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目实施人员分配Controller
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController
@RequestMapping("/wk/implementer")
public class WkProjectImplementerController extends BaseController
{
    @Autowired
    private IWkProjectImplementerService wkProjectImplementerService;

    /**
     * 查询项目实施人员分配列表
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:list')")
    @GetMapping("/list")
    public TableDataInfo list(WkProjectImplementer wkProjectImplementer)
    {
        startPage();
        List<WkProjectImplementer> list = wkProjectImplementerService.selectWkProjectImplementerList(wkProjectImplementer);
        return getDataTable(list);
    }

    /**
     * 根据项目ID查询实施人员分配列表
     */
    @GetMapping("/listByProject/{projectId}")
    public AjaxResult listByProject(@PathVariable("projectId") Long projectId)
    {
        List<WkProjectImplementer> list = wkProjectImplementerService.selectImplementersByProjectId(projectId);
        return success(list);
    }

    /**
     * 根据用户ID查询已分配的项目列表
     */
    @GetMapping("/listByUser/{userId}")
    public AjaxResult listByUser(@PathVariable("userId") Long userId)
    {
        List<WkProjectImplementer> list = wkProjectImplementerService.selectProjectsByUserId(userId);
        return success(list);
    }

    /**
     * 导出项目实施人员分配列表
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:export')")
    @Log(title = "项目实施人员分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WkProjectImplementer wkProjectImplementer)
    {
        List<WkProjectImplementer> list = wkProjectImplementerService.selectWkProjectImplementerList(wkProjectImplementer);
        ExcelUtil<WkProjectImplementer> util = new ExcelUtil<WkProjectImplementer>(WkProjectImplementer.class);
        util.exportExcel(response, list, "项目实施人员分配数据");
    }

    /**
     * 获取项目实施人员分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:query')")
    @GetMapping(value = "/{implementerId}")
    public AjaxResult getInfo(@PathVariable("implementerId") Long implementerId)
    {
        return success(wkProjectImplementerService.selectWkProjectImplementerByImplementerId(implementerId));
    }

    /**
     * 新增项目实施人员分配
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:add')")
    @Log(title = "项目实施人员分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WkProjectImplementer wkProjectImplementer)
    {
        return toAjax(wkProjectImplementerService.insertWkProjectImplementer(wkProjectImplementer));
    }

    /**
     * 为项目分配实施人员
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:assign')")
    @Log(title = "项目实施人员分配", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public AjaxResult assignImplementers(@RequestBody ProjectImplementerAssignDto assignDto)
    {
        // 验证参数
        if (assignDto.getProjectId() == null) {
            return error("项目ID不能为空");
        }
        
        if (assignDto.getForwardUserId() == null && assignDto.getReverseUserId() == null) {
            return error("至少需要分配一个实施人员");
        }
        
        // 检查是否分配了相同的用户
        if (assignDto.getForwardUserId() != null && assignDto.getReverseUserId() != null 
            && assignDto.getForwardUserId().equals(assignDto.getReverseUserId())) {
            return error("正序和逆序不能分配给同一个用户");
        }
        
        int result = wkProjectImplementerService.assignImplementersToProject(assignDto);
        if (result > 0) {
            return success("分配成功");
        } else {
            return error("分配失败");
        }
    }

    /**
     * 自动分配工单给实施人员
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:autoAssign')")
    @Log(title = "自动分配工单", businessType = BusinessType.UPDATE)
    @PostMapping("/autoAssignTasks/{projectId}")
    public AjaxResult autoAssignTasks(@PathVariable("projectId") Long projectId)
    {
        int assignedCount = wkProjectImplementerService.autoAssignTasksToImplementers(projectId);
        return success("成功分配 " + assignedCount + " 个工单");
    }

    /**
     * 检查用户是否已分配到项目
     */
    @GetMapping("/checkAssigned/{projectId}/{userId}")
    public AjaxResult checkUserAssigned(@PathVariable("projectId") Long projectId, @PathVariable("userId") Long userId)
    {
        boolean isAssigned = wkProjectImplementerService.isUserAssignedToProject(projectId, userId);
        return success(isAssigned);
    }

    /**
     * 获取项目的实施人员数量
     */
    @GetMapping("/count/{projectId}")
    public AjaxResult getImplementerCount(@PathVariable("projectId") Long projectId)
    {
        int count = wkProjectImplementerService.getImplementerCountByProjectId(projectId);
        return success(count);
    }

    /**
     * 修改项目实施人员分配
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:edit')")
    @Log(title = "项目实施人员分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WkProjectImplementer wkProjectImplementer)
    {
        return toAjax(wkProjectImplementerService.updateWkProjectImplementer(wkProjectImplementer));
    }

    /**
     * 删除项目实施人员分配
     */
    @PreAuthorize("@ss.hasPermi('wk:implementer:remove')")
    @Log(title = "项目实施人员分配", businessType = BusinessType.DELETE)
	@DeleteMapping("/{implementerIds}")
    public AjaxResult remove(@PathVariable Long[] implementerIds)
    {
        return toAjax(wkProjectImplementerService.deleteWkProjectImplementerByImplementerIds(implementerIds));
    }
}
