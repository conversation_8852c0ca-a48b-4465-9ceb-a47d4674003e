package com.xz.worker.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.xz.worker.domain.WkTask;
import com.xz.worker.domain.WkProject;
import com.xz.worker.service.IWkTaskService;
import com.xz.worker.service.IWkProjectService;
import com.xz.worker.mapper.WkTaskMapper;

/**
 * 工单分配Controller
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController
@RequestMapping("/wk/taskAssign")
public class WkTaskAssignController extends BaseController
{
    @Autowired
    private IWkTaskService wkTaskService;
    
    @Autowired
    private IWkProjectService wkProjectService;
    
    @Autowired
    private WkTaskMapper wkTaskMapper;

    /**
     * 查询项目列表（带未分配工单数量）
     */
    @GetMapping("/projects")
    public AjaxResult getProjectsWithTaskCount(WkProject wkProject)
    {
        startPage();
        List<WkProject> projects = wkProjectService.selectWkProjectList(wkProject);
        
        // 为每个项目添加未分配工单数量
        for (WkProject project : projects) {
            int unassignedCount = getUnassignedTaskCount(project.getProjectId());
            project.setRemark(String.valueOf(unassignedCount)); // 临时使用remark字段存储未分配工单数
        }
        
        return success(getDataTable(projects));
    }

    /**
     * 获取项目下未分配的工单数量
     */
    @GetMapping("/unassignedCount/{projectId}")
    public AjaxResult getUnassignedTaskCount(@PathVariable("projectId") Long projectId)
    {
        int count = getUnassignedTaskCount(projectId);
        return success(count);
    }

    /**
     * 获取项目下未分配的工单ID列表
     */
    @GetMapping("/unassignedTasks/{projectId}")
    public AjaxResult getUnassignedTaskIds(@PathVariable("projectId") Long projectId)
    {
        WkTask queryTask = new WkTask();
        queryTask.setProjectId(projectId);
        queryTask.setAttachStatus(0); // 0表示未分配的工单（principal_id is null）
        
        List<WkTask> unassignedTasks = wkTaskMapper.selectWkTaskList(queryTask);
        List<Long> taskIds = new ArrayList<>();
        for (WkTask task : unassignedTasks) {
            taskIds.add(task.getTaskId());
        }
        
        return success(taskIds);
    }

    /**
     * 自动分配工单给单个实施人员
     */
    @Log(title = "自动分配工单", businessType = BusinessType.UPDATE)
    @PostMapping("/assignToSingle")
    public AjaxResult assignToSingleUser(@RequestBody Map<String, Object> params)
    {
        Long projectId = Long.valueOf(params.get("projectId").toString());
        Long userId = Long.valueOf(params.get("userId").toString());
        
        // 获取项目下未分配的工单
        WkTask queryTask = new WkTask();
        queryTask.setProjectId(projectId);
        queryTask.setAttachStatus(0); // 未分配的工单
        
        List<WkTask> unassignedTasks = wkTaskMapper.selectWkTaskList(queryTask);
        
        if (unassignedTasks.isEmpty()) {
            return error("该项目下没有未分配的工单");
        }
        
        // 批量分配工单
        List<Long> taskIds = new ArrayList<>();
        for (WkTask task : unassignedTasks) {
            taskIds.add(task.getTaskId());
        }
        
        int result = wkTaskMapper.batchAttach(userId, taskIds);
        
        if (result > 0) {
            return success("成功分配 " + result + " 个工单给实施人员");
        } else {
            return error("工单分配失败");
        }
    }

    /**
     * 自动分配工单给两个实施人员（平均分配）
     */
    @Log(title = "自动分配工单", businessType = BusinessType.UPDATE)
    @PostMapping("/assignToTwo")
    public AjaxResult assignToTwoUsers(@RequestBody Map<String, Object> params)
    {
        Long projectId = Long.valueOf(params.get("projectId").toString());
        Long forwardUserId = Long.valueOf(params.get("forwardUserId").toString());
        Long reverseUserId = Long.valueOf(params.get("reverseUserId").toString());
        
        if (forwardUserId.equals(reverseUserId)) {
            return error("正序和逆序不能分配给同一个用户");
        }
        
        // 获取项目下未分配的工单
        WkTask queryTask = new WkTask();
        queryTask.setProjectId(projectId);
        queryTask.setAttachStatus(0); // 未分配的工单
        
        List<WkTask> unassignedTasks = wkTaskMapper.selectWkTaskList(queryTask);
        
        if (unassignedTasks.isEmpty()) {
            return error("该项目下没有未分配的工单");
        }
        
        int totalTasks = unassignedTasks.size();
        int halfSize = totalTasks / 2;
        
        int assignedCount = 0;
        
        // 正序实施人员分配前一半
        if (halfSize > 0) {
            List<Long> forwardTaskIds = new ArrayList<>();
            for (int i = 0; i < halfSize; i++) {
                forwardTaskIds.add(unassignedTasks.get(i).getTaskId());
            }
            int forwardResult = wkTaskMapper.batchAttach(forwardUserId, forwardTaskIds);
            assignedCount += forwardResult;
        }
        
        // 逆序实施人员分配后一半
        if (totalTasks > halfSize) {
            List<Long> reverseTaskIds = new ArrayList<>();
            for (int i = halfSize; i < totalTasks; i++) {
                reverseTaskIds.add(unassignedTasks.get(i).getTaskId());
            }
            int reverseResult = wkTaskMapper.batchAttach(reverseUserId, reverseTaskIds);
            assignedCount += reverseResult;
        }
        
        if (assignedCount > 0) {
            return success("成功分配 " + assignedCount + " 个工单，正序实施人员分配 " + halfSize + " 个，逆序实施人员分配 " + (totalTasks - halfSize) + " 个");
        } else {
            return error("工单分配失败");
        }
    }

    /**
     * 获取用户已分配的项目列表（简化版）
     */
    @GetMapping("/userProjects/{userId}")
    public AjaxResult getUserProjects(@PathVariable("userId") Long userId)
    {
        // 查询用户作为实施人员的工单所属项目
        List<Map<String, Object>> userProjects = new ArrayList<>();
        
        // 这里可以查询用户已分配的工单，然后统计项目
        WkTask queryTask = new WkTask();
        queryTask.setPrincipalId(userId);
        List<WkTask> userTasks = wkTaskMapper.selectWkTaskList(queryTask);
        
        // 统计项目
        Map<Long, String> projectMap = new HashMap<>();
        for (WkTask task : userTasks) {
            if (task.getProjectId() != null && !projectMap.containsKey(task.getProjectId())) {
                WkProject project = wkProjectService.selectWkProjectByProjectId(task.getProjectId());
                if (project != null) {
                    projectMap.put(task.getProjectId(), project.getName());
                }
            }
        }
        
        // 转换为前端需要的格式
        for (Map.Entry<Long, String> entry : projectMap.entrySet()) {
            Map<String, Object> projectInfo = new HashMap<>();
            projectInfo.put("projectId", entry.getKey());
            projectInfo.put("projectName", entry.getValue());
            userProjects.add(projectInfo);
        }
        
        return success(userProjects);
    }

    /**
     * 获取项目下未分配工单数量的私有方法
     */
    private int getUnassignedTaskCount(Long projectId) {
        WkTask queryTask = new WkTask();
        queryTask.setProjectId(projectId);
        queryTask.setAttachStatus(0); // 0表示未分配的工单
        
        List<WkTask> unassignedTasks = wkTaskMapper.selectWkTaskList(queryTask);
        return unassignedTasks.size();
    }
}
