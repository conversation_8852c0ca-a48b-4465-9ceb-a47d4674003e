package com.xz.worker.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.xz.worker.mapper.WkProjectImplementerMapper;
import com.xz.worker.mapper.WkTaskMapper;
import com.xz.worker.domain.WkProjectImplementer;
import com.xz.worker.domain.WkTask;
import com.xz.worker.domain.dto.ProjectImplementerAssignDto;
import com.xz.worker.service.IWkProjectImplementerService;

/**
 * 项目实施人员分配Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class WkProjectImplementerServiceImpl implements IWkProjectImplementerService
{
    private static final Logger log = LoggerFactory.getLogger(WkProjectImplementerServiceImpl.class);

    @Autowired
    private WkProjectImplementerMapper wkProjectImplementerMapper;

    @Autowired
    private WkTaskMapper wkTaskMapper;

    /**
     * 查询项目实施人员分配
     * 
     * @param implementerId 项目实施人员分配主键
     * @return 项目实施人员分配
     */
    @Override
    public WkProjectImplementer selectWkProjectImplementerByImplementerId(Long implementerId)
    {
        return wkProjectImplementerMapper.selectWkProjectImplementerByImplementerId(implementerId);
    }

    /**
     * 查询项目实施人员分配列表
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 项目实施人员分配
     */
    @Override
    public List<WkProjectImplementer> selectWkProjectImplementerList(WkProjectImplementer wkProjectImplementer)
    {
        return wkProjectImplementerMapper.selectWkProjectImplementerList(wkProjectImplementer);
    }

    /**
     * 根据项目ID查询实施人员分配列表
     * 
     * @param projectId 项目ID
     * @return 项目实施人员分配集合
     */
    @Override
    public List<WkProjectImplementer> selectImplementersByProjectId(Long projectId)
    {
        return wkProjectImplementerMapper.selectImplementersByProjectId(projectId);
    }

    /**
     * 根据用户ID查询已分配的项目列表
     * 
     * @param userId 用户ID
     * @return 项目实施人员分配集合
     */
    @Override
    public List<WkProjectImplementer> selectProjectsByUserId(Long userId)
    {
        return wkProjectImplementerMapper.selectProjectsByUserId(userId);
    }

    /**
     * 新增项目实施人员分配
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 结果
     */
    @Override
    public int insertWkProjectImplementer(WkProjectImplementer wkProjectImplementer)
    {
        wkProjectImplementer.setCreateTime(DateUtils.getNowDate());
        wkProjectImplementer.setCreateBy(SecurityUtils.getUsername());
        wkProjectImplementer.setDelFlag("0");
        wkProjectImplementer.setStatus(1);
        wkProjectImplementer.setAssignTime(new Date());
        return wkProjectImplementerMapper.insertWkProjectImplementer(wkProjectImplementer);
    }

    /**
     * 修改项目实施人员分配
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 结果
     */
    @Override
    public int updateWkProjectImplementer(WkProjectImplementer wkProjectImplementer)
    {
        wkProjectImplementer.setUpdateTime(DateUtils.getNowDate());
        wkProjectImplementer.setUpdateBy(SecurityUtils.getUsername());
        return wkProjectImplementerMapper.updateWkProjectImplementer(wkProjectImplementer);
    }

    /**
     * 批量删除项目实施人员分配
     * 
     * @param implementerIds 需要删除的项目实施人员分配主键
     * @return 结果
     */
    @Override
    public int deleteWkProjectImplementerByImplementerIds(Long[] implementerIds)
    {
        return wkProjectImplementerMapper.deleteWkProjectImplementerByImplementerIds(implementerIds);
    }

    /**
     * 删除项目实施人员分配信息
     * 
     * @param implementerId 项目实施人员分配主键
     * @return 结果
     */
    @Override
    public int deleteWkProjectImplementerByImplementerId(Long implementerId)
    {
        return wkProjectImplementerMapper.deleteWkProjectImplementerByImplementerId(implementerId);
    }

    /**
     * 为项目分配实施人员
     * 
     * @param assignDto 分配信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignImplementersToProject(ProjectImplementerAssignDto assignDto)
    {
        log.info("开始为项目{}分配实施人员，正序用户：{}，逆序用户：{}",
                assignDto.getProjectId(), assignDto.getForwardUserId(), assignDto.getReverseUserId());

        // 先删除项目原有的实施人员分配
        wkProjectImplementerMapper.deleteImplementersByProjectId(assignDto.getProjectId());
        
        List<WkProjectImplementer> implementers = new ArrayList<>();
        
        // 添加正序实施人员
        if (assignDto.getForwardUserId() != null) {
            WkProjectImplementer forwardImplementer = new WkProjectImplementer();
            forwardImplementer.setProjectId(assignDto.getProjectId());
            forwardImplementer.setUserId(assignDto.getForwardUserId());
            forwardImplementer.setExecutionOrder(1); // 正序
            forwardImplementer.setStatus(1);
            forwardImplementer.setDelFlag("0");
            forwardImplementer.setAssignTime(new Date());
            forwardImplementer.setCreateBy(SecurityUtils.getUsername());
            forwardImplementer.setCreateTime(DateUtils.getNowDate());
            implementers.add(forwardImplementer);
        }
        
        // 添加逆序实施人员
        if (assignDto.getReverseUserId() != null) {
            WkProjectImplementer reverseImplementer = new WkProjectImplementer();
            reverseImplementer.setProjectId(assignDto.getProjectId());
            reverseImplementer.setUserId(assignDto.getReverseUserId());
            reverseImplementer.setExecutionOrder(2); // 逆序
            reverseImplementer.setStatus(1);
            reverseImplementer.setDelFlag("0");
            reverseImplementer.setAssignTime(new Date());
            reverseImplementer.setCreateBy(SecurityUtils.getUsername());
            reverseImplementer.setCreateTime(DateUtils.getNowDate());
            implementers.add(reverseImplementer);
        }
        
        int result = 0;
        if (!implementers.isEmpty()) {
            result = wkProjectImplementerMapper.batchInsertImplementers(implementers);
            log.info("项目{}实施人员分配完成，分配{}个实施人员", assignDto.getProjectId(), implementers.size());

            // 如果需要自动分配工单
            if (assignDto.getAutoAssignTasks() != null && assignDto.getAutoAssignTasks()) {
                int assignedTasks = autoAssignTasksToImplementers(assignDto.getProjectId());
                log.info("项目{}自动分配工单完成，分配{}个工单", assignDto.getProjectId(), assignedTasks);
            }
        }

        return result;
    }

    /**
     * 自动分配工单给实施人员
     * 
     * @param projectId 项目ID
     * @return 分配的工单数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int autoAssignTasksToImplementers(Long projectId)
    {
        // 获取项目的实施人员
        List<WkProjectImplementer> implementers = selectImplementersByProjectId(projectId);
        if (implementers.isEmpty()) {
            return 0;
        }
        
        // 获取项目下未分配的工单
        WkTask queryTask = new WkTask();
        queryTask.setProjectId(projectId);
        queryTask.setAttachStatus(0); // 0表示未分配的工单（principal_id is null）
        List<WkTask> unassignedTasks = wkTaskMapper.selectWkTaskList(queryTask);
        
        if (unassignedTasks.isEmpty()) {
            return 0;
        }
        
        int assignedCount = 0;
        
        if (implementers.size() == 1) {
            // 只有一个实施人员，全部分配给他
            WkProjectImplementer implementer = implementers.get(0);
            List<Long> taskIds = new ArrayList<>();
            for (WkTask task : unassignedTasks) {
                taskIds.add(task.getTaskId());
            }
            wkTaskMapper.batchAttach(implementer.getUserId(), taskIds);
            assignedCount = taskIds.size();
        } else if (implementers.size() == 2) {
            // 两个实施人员，平均分配
            WkProjectImplementer forwardImplementer = null;
            WkProjectImplementer reverseImplementer = null;
            
            for (WkProjectImplementer impl : implementers) {
                if (impl.getExecutionOrder() == 1) {
                    forwardImplementer = impl;
                } else if (impl.getExecutionOrder() == 2) {
                    reverseImplementer = impl;
                }
            }
            
            if (forwardImplementer != null && reverseImplementer != null) {
                int totalTasks = unassignedTasks.size();
                int halfSize = totalTasks / 2;
                
                // 正序实施人员分配前一半
                List<Long> forwardTaskIds = new ArrayList<>();
                for (int i = 0; i < halfSize; i++) {
                    forwardTaskIds.add(unassignedTasks.get(i).getTaskId());
                }
                if (!forwardTaskIds.isEmpty()) {
                    wkTaskMapper.batchAttach(forwardImplementer.getUserId(), forwardTaskIds);
                    assignedCount += forwardTaskIds.size();
                }
                
                // 逆序实施人员分配后一半
                List<Long> reverseTaskIds = new ArrayList<>();
                for (int i = halfSize; i < totalTasks; i++) {
                    reverseTaskIds.add(unassignedTasks.get(i).getTaskId());
                }
                if (!reverseTaskIds.isEmpty()) {
                    wkTaskMapper.batchAttach(reverseImplementer.getUserId(), reverseTaskIds);
                    assignedCount += reverseTaskIds.size();
                }
            }
        }
        
        return assignedCount;
    }

    /**
     * 检查用户是否已分配到项目
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否已分配
     */
    @Override
    public boolean isUserAssignedToProject(Long projectId, Long userId)
    {
        return wkProjectImplementerMapper.checkUserAssigned(projectId, userId) > 0;
    }

    /**
     * 获取项目的实施人员数量
     * 
     * @param projectId 项目ID
     * @return 实施人员数量
     */
    @Override
    public int getImplementerCountByProjectId(Long projectId)
    {
        return wkProjectImplementerMapper.countImplementersByProjectId(projectId);
    }
}
