package com.xz.worker.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.xz.worker.domain.WkCityLabel;
import com.xz.worker.domain.WkTask;
import com.xz.worker.domain.dto.CLabelTreeNode;
import com.xz.worker.domain.dto.UpdateIsRushTask;
import com.xz.worker.mapper.WkCityLabelMapper;
import com.xz.worker.mapper.WkTaskMapper;
import com.xz.worker.mapper.WkProjectImplementerMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xz.worker.mapper.WkProjectMapper;
import com.xz.worker.domain.WkProject;
import com.xz.worker.service.IWkProjectService;

/**
 * 项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-11
 */
@Service
public class WkProjectServiceImpl implements IWkProjectService 
{
    @Autowired
    private WkProjectMapper wkProjectMapper;

    @Autowired
    private WkCityLabelMapper wkCityLabelMapper;

    @Autowired
    private WkTaskMapper wkTaskMapper;

    @Autowired
    private WkProjectImplementerMapper wkProjectImplementerMapper;

    /**
     * 查询项目
     * 
     * @param projectId 项目主键
     * @return 项目
     */
    @Override
    public WkProject selectWkProjectByProjectId(Long projectId)
    {
        return wkProjectMapper.selectWkProjectByProjectId(projectId);
    }

    /**
     * 查询项目列表
     * 
     * @param wkProject 项目
     * @return 项目
     */
    @Override
    public List<WkProject> selectWkProjectList(WkProject wkProject)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        boolean audit2Flg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(100l) ? true : false;
        boolean audit1Flg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(103l) ? true : false;
        boolean reviewFlg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(101l) ? true : false;
        boolean commonFlg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(2l) ? true : false;

        // 如果是二审人员
        if (audit2Flg || audit1Flg || reviewFlg || commonFlg) {
            WkTask wkTask = new WkTask();
            Set<Long> projectIds = new HashSet<>();
            if (audit2Flg) {
                wkTask.setAudit2byId(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setAudit2byId(null);
            }

            if (audit1Flg) {
                wkTask.setAudit1byId(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setAudit1byId(null);
            }

            if (reviewFlg) {
                wkTask.setReviewById(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setReviewById(null);
            }

            if (commonFlg) {
                wkTask.setPrincipalId(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setPrincipalId(null);
            }

            return wkProjectMapper.selectWkProjectList(wkProject).stream().filter(o->projectIds.contains(o.getProjectId())).sorted(Comparator.comparing(WkProject::getProjectId).reversed()).collect(Collectors.toList());
        } else {
            return wkProjectMapper.selectWkProjectList(wkProject);
        }
    }

    @Override
    public List<WkProject> selectWkProjectListV1(WkProject wkProject)
    {
        wkProject.setYesterday(DateUtils.parseDateToStr("yyyyMMdd", DateUtils.addDays(new Date(), -1)));
        LoginUser loginUser = SecurityUtils.getLoginUser();
        boolean audit2Flg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(100l) ? true : false;
        boolean audit1Flg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(103l) ? true : false;
        boolean reviewFlg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(101l) ? true : false;
        boolean commonFlg = loginUser.getUser().getRoles().stream().map(o->o.getRoleId()).collect(Collectors.toList()).contains(2l) ? true : false;

        // 如果是二审人员
        if (audit2Flg || audit1Flg || reviewFlg || commonFlg) {
            WkTask wkTask = new WkTask();
            Set<Long> projectIds = new HashSet<>();
            if (audit2Flg) {
                wkTask.setAudit2byId(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setAudit2byId(null);
            }

            if (audit1Flg) {
                wkTask.setAudit1byId(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setAudit1byId(null);
            }

            if (reviewFlg) {
                wkTask.setReviewById(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setReviewById(null);
            }

            if (commonFlg) {
                wkTask.setPrincipalId(loginUser.getUserId());
                projectIds.addAll(wkTaskMapper.selectWkTaskIds(wkTask).stream().map(o->o.getProjectId()).collect(Collectors.toList()));
                wkTask.setPrincipalId(null);
            }

            return wkProjectMapper.selectWkProjectListV1(wkProject).stream().filter(o->projectIds.contains(o.getProjectId())).collect(Collectors.toList());
        } else {
            return wkProjectMapper.selectWkProjectListV1(wkProject);
        }
    }

    @Override
    public List<WkProject> selectWkProjectListV2(WkProject wkProject) {
        return wkProjectMapper.selectWkProjectList(wkProject);
    }

    @Override
    public List<WkProject> getGisProjects() {
        return wkProjectMapper.getGisProjects();
    }

    @Override
    public List<Long> selectWkProjectIds(WkProject wkProject) {
        return wkProjectMapper.selectWkProjectIds(wkProject);
    }

    @Override
    public List<WkCityLabel> getProjectIndexs(Long projectId) {

        // 查询所有的指标节点
//        List<WkCityLabel> labelList = wkCityLabelMapper.selectWkCityLabelList(new WkCityLabel());
//        List<CLabelTreeNode> nodes = new ArrayList<>();
//        labelList.forEach(o-> {
//            nodes.add(new CLabelTreeNode(o.getClabelId(), o.getParentId(), o.getName()));
//        });

        // 查询项目的指标组
        List<WkCityLabel> plabels = wkCityLabelMapper.getProjectIndexs(projectId);

//        CLabelTreeNode cLabelTreeNode = new CLabelTreeNode();
//        List<CLabelTreeNode> newnodes = new ArrayList<>();
//        plabels.forEach(o->{
//            List<CLabelTreeNode> filteredNodes = cLabelTreeNode.findNodesByChildIdAndParentId(nodes, o.getClabelId(), null);
//            newnodes.addAll(filteredNodes);
//        });

        return plabels;
    }

    /**
     * 新增项目
     * 
     * @param wkProject 项目
     * @return 结果
     */
    @Override
    public int insertWkProject(WkProject wkProject)
    {
        wkProject.setCreateTime(DateUtils.getNowDate());
        return wkProjectMapper.insertWkProject(wkProject);
    }

    /**
     * 修改项目
     * 
     * @param wkProject 项目
     * @return 结果
     */
    @Override
    public int updateWkProject(WkProject wkProject)
    {
        wkProject.setUpdateTime(DateUtils.getNowDate());
        return wkProjectMapper.updateWkProject(wkProject);
    }

    /**
     * 批量删除项目
     * 
     * @param projectIds 需要删除的项目主键
     * @return 结果
     */
    @Override
    public int deleteWkProjectByProjectIds(Long[] projectIds)
    {
        return wkProjectMapper.deleteWkProjectByProjectIds(projectIds);
    }

    @Override
    public int batchUpdateIsRushTask(UpdateIsRushTask updateIsRushTask) {
        return wkProjectMapper.batchUpdateIsRushTask(updateIsRushTask);
    }

    /**
     * 删除项目信息
     * 
     * @param projectId 项目主键
     * @return 结果
     */
    @Override
    public int deleteWkProjectByProjectId(Long projectId)
    {
        return wkProjectMapper.deleteWkProjectByProjectId(projectId);
    }
}
