package com.xz.worker.service;

import java.util.List;
import com.xz.worker.domain.WkProjectImplementer;
import com.xz.worker.domain.dto.ProjectImplementerAssignDto;

/**
 * 项目实施人员分配Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface IWkProjectImplementerService 
{
    /**
     * 查询项目实施人员分配
     * 
     * @param implementerId 项目实施人员分配主键
     * @return 项目实施人员分配
     */
    public WkProjectImplementer selectWkProjectImplementerByImplementerId(Long implementerId);

    /**
     * 查询项目实施人员分配列表
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 项目实施人员分配集合
     */
    public List<WkProjectImplementer> selectWkProjectImplementerList(WkProjectImplementer wkProjectImplementer);

    /**
     * 根据项目ID查询实施人员分配列表
     * 
     * @param projectId 项目ID
     * @return 项目实施人员分配集合
     */
    public List<WkProjectImplementer> selectImplementersByProjectId(Long projectId);

    /**
     * 根据用户ID查询已分配的项目列表
     * 
     * @param userId 用户ID
     * @return 项目实施人员分配集合
     */
    public List<WkProjectImplementer> selectProjectsByUserId(Long userId);

    /**
     * 新增项目实施人员分配
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 结果
     */
    public int insertWkProjectImplementer(WkProjectImplementer wkProjectImplementer);

    /**
     * 修改项目实施人员分配
     * 
     * @param wkProjectImplementer 项目实施人员分配
     * @return 结果
     */
    public int updateWkProjectImplementer(WkProjectImplementer wkProjectImplementer);

    /**
     * 批量删除项目实施人员分配
     * 
     * @param implementerIds 需要删除的项目实施人员分配主键集合
     * @return 结果
     */
    public int deleteWkProjectImplementerByImplementerIds(Long[] implementerIds);

    /**
     * 删除项目实施人员分配信息
     * 
     * @param implementerId 项目实施人员分配主键
     * @return 结果
     */
    public int deleteWkProjectImplementerByImplementerId(Long implementerId);

    /**
     * 为项目分配实施人员
     * 
     * @param assignDto 分配信息
     * @return 结果
     */
    public int assignImplementersToProject(ProjectImplementerAssignDto assignDto);

    /**
     * 自动分配工单给实施人员
     * 
     * @param projectId 项目ID
     * @return 分配的工单数量
     */
    public int autoAssignTasksToImplementers(Long projectId);

    /**
     * 检查用户是否已分配到项目
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否已分配
     */
    public boolean isUserAssignedToProject(Long projectId, Long userId);

    /**
     * 获取项目的实施人员数量
     * 
     * @param projectId 项目ID
     * @return 实施人员数量
     */
    public int getImplementerCountByProjectId(Long projectId);
}
