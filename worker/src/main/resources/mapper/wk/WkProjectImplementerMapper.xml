<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xz.worker.mapper.WkProjectImplementerMapper">
    
    <resultMap type="WkProjectImplementer" id="WkProjectImplementerResult">
        <result property="implementerId"    column="implementer_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="userId"    column="user_id"    />
        <result property="executionOrder"    column="execution_order"    />
        <result property="assignTime"    column="assign_time"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
    </resultMap>

    <sql id="selectWkProjectImplementerVo">
        select implementer_id, project_id, user_id, execution_order, assign_time, status, del_flag, create_by, create_time, update_by, update_time, tenant_id from wk_project_implementer
    </sql>

    <select id="selectWkProjectImplementerList" parameterType="WkProjectImplementer" resultMap="WkProjectImplementerResult">
        SELECT 
            i.implementer_id, i.project_id, i.user_id, i.execution_order, i.assign_time, i.status, 
            i.del_flag, i.create_by, i.create_time, i.update_by, i.update_time, i.tenant_id,
            p.name as project_name,
            u.user_name,
            u.nick_name
        FROM wk_project_implementer i
        LEFT JOIN wk_project p ON p.project_id = i.project_id
        LEFT JOIN sys_user u ON u.user_id = i.user_id
        <where>  
            i.del_flag = '0'
            <if test="projectId != null "> and i.project_id = #{projectId}</if>
            <if test="userId != null "> and i.user_id = #{userId}</if>
            <if test="executionOrder != null "> and i.execution_order = #{executionOrder}</if>
            <if test="status != null "> and i.status = #{status}</if>
        </where>
        ORDER BY i.project_id, i.execution_order
    </select>
    
    <select id="selectWkProjectImplementerByImplementerId" parameterType="Long" resultMap="WkProjectImplementerResult">
        SELECT 
            i.implementer_id, i.project_id, i.user_id, i.execution_order, i.assign_time, i.status, 
            i.del_flag, i.create_by, i.create_time, i.update_by, i.update_time, i.tenant_id,
            p.name as project_name,
            u.user_name,
            u.nick_name
        FROM wk_project_implementer i
        LEFT JOIN wk_project p ON p.project_id = i.project_id
        LEFT JOIN sys_user u ON u.user_id = i.user_id
        WHERE i.implementer_id = #{implementerId} AND i.del_flag = '0'
    </select>

    <select id="selectImplementersByProjectId" parameterType="Long" resultMap="WkProjectImplementerResult">
        SELECT 
            i.implementer_id, i.project_id, i.user_id, i.execution_order, i.assign_time, i.status, 
            i.del_flag, i.create_by, i.create_time, i.update_by, i.update_time, i.tenant_id,
            p.name as project_name,
            u.user_name,
            u.nick_name
        FROM wk_project_implementer i
        LEFT JOIN wk_project p ON p.project_id = i.project_id
        LEFT JOIN sys_user u ON u.user_id = i.user_id
        WHERE i.project_id = #{projectId} AND i.del_flag = '0' AND i.status = 1
        ORDER BY i.execution_order
    </select>

    <select id="selectProjectsByUserId" parameterType="Long" resultMap="WkProjectImplementerResult">
        SELECT 
            i.implementer_id, i.project_id, i.user_id, i.execution_order, i.assign_time, i.status, 
            i.del_flag, i.create_by, i.create_time, i.update_by, i.update_time, i.tenant_id,
            p.name as project_name,
            u.user_name,
            u.nick_name
        FROM wk_project_implementer i
        LEFT JOIN wk_project p ON p.project_id = i.project_id
        LEFT JOIN sys_user u ON u.user_id = i.user_id
        WHERE i.user_id = #{userId} AND i.del_flag = '0' AND i.status = 1
        ORDER BY i.assign_time DESC
    </select>

    <select id="checkUserAssigned" resultType="int">
        SELECT COUNT(1) FROM wk_project_implementer 
        WHERE project_id = #{projectId} AND user_id = #{userId} AND del_flag = '0' AND status = 1
    </select>

    <select id="countImplementersByProjectId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM wk_project_implementer 
        WHERE project_id = #{projectId} AND del_flag = '0' AND status = 1
    </select>
        
    <insert id="insertWkProjectImplementer" parameterType="WkProjectImplementer" useGeneratedKeys="true" keyProperty="implementerId">
        insert into wk_project_implementer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="executionOrder != null">execution_order,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="executionOrder != null">#{executionOrder},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <update id="updateWkProjectImplementer" parameterType="WkProjectImplementer">
        update wk_project_implementer
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="executionOrder != null">execution_order = #{executionOrder},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where implementer_id = #{implementerId}
    </update>

    <delete id="deleteWkProjectImplementerByImplementerId" parameterType="Long">
        update wk_project_implementer set del_flag = '2' where implementer_id = #{implementerId}
    </delete>

    <delete id="deleteWkProjectImplementerByImplementerIds" parameterType="String">
        update wk_project_implementer set del_flag = '2' where implementer_id in 
        <foreach item="implementerId" collection="array" open="(" separator="," close=")">
            #{implementerId}
        </foreach>
    </delete>

    <delete id="deleteImplementersByProjectId" parameterType="Long">
        update wk_project_implementer set del_flag = '2' where project_id = #{projectId}
    </delete>

    <insert id="batchInsertImplementers" parameterType="java.util.List">
        insert into wk_project_implementer(project_id, user_id, execution_order, assign_time, status, del_flag, create_by, create_time, tenant_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId}, #{item.userId}, #{item.executionOrder}, #{item.assignTime}, #{item.status}, #{item.delFlag}, #{item.createBy}, #{item.createTime}, #{item.tenantId})
        </foreach>
    </insert>
</mapper>
