# 工单自动分配功能说明

## 功能概述

这是一个简化的工单自动分配功能，专门用于将项目下的未分配工单自动分配给实施人员。支持两种分配模式：分配给单个实施人员或平均分配给两个实施人员。

## 主要特性

1. **项目筛选**：支持按项目名称、项目类型（人查）、状态（正常）、开始时间、结束时间进行筛选
2. **未分配工单统计**：实时显示每个项目下未分配的工单数量
3. **灵活分配模式**：
   - 单个实施人员：所有未分配工单分配给选择的实施人员
   - 两个实施人员：未分配工单平均分配，正序实施人员分配前一半，逆序实施人员分配后一半
4. **已分配项目提示**：显示实施人员已分配的其他项目信息

## 文件结构

### 后端文件
- `worker/src/main/java/com/xz/worker/controller/WkTaskAssignController.java` - 工单分配控制器
- `sql/task_assign_menu.sql` - 菜单配置SQL脚本

### 前端文件
- `ruoyi-ui/src/views/wk/taskAssign/index.vue` - 工单分配页面
- `ruoyi-ui/src/api/wk/taskAssign.js` - API接口封装

## 主要API接口

1. **GET /wk/taskAssign/projects** - 查询项目列表（带未分配工单数量）
2. **GET /wk/taskAssign/unassignedCount/{projectId}** - 获取项目下未分配工单数量
3. **POST /wk/taskAssign/assignToSingle** - 分配工单给单个实施人员
4. **POST /wk/taskAssign/assignToTwo** - 分配工单给两个实施人员
5. **GET /wk/taskAssign/userProjects/{userId}** - 获取用户已分配的项目列表

## 业务逻辑

### 工单分配规则

1. **单个实施人员模式**：
   - 所有未分配工单全部分配给选择的实施人员
   - 适用于小型项目或专人负责的项目

2. **两个实施人员模式**：
   - 未分配工单按数量平均分配
   - 正序实施人员：分配前一半工单
   - 逆序实施人员：分配后一半工单
   - 如果工单数量为奇数，逆序实施人员会多分配一个工单

### 验证规则

1. 项目必须有未分配的工单才能进行分配
2. 两个实施人员模式下，正序和逆序不能是同一个用户
3. 必须选择至少一个实施人员

## 部署步骤

### 1. 数据库配置
```sql
-- 查询工单管理菜单的ID
SELECT menu_id, menu_name, parent_id FROM sys_menu WHERE menu_name LIKE '%工单管理%';

-- 根据查询结果修改 sql/task_assign_menu.sql 中的 parent_id，然后执行
source sql/task_assign_menu.sql;
```

### 2. 后端部署
确保以下文件已正确部署：
- `worker/src/main/java/com/xz/worker/controller/WkTaskAssignController.java`

### 3. 前端部署
确保以下文件已正确部署：
- `ruoyi-ui/src/api/wk/taskAssign.js`
- `ruoyi-ui/src/views/wk/taskAssign/index.vue`

### 4. 权限配置
在系统管理 -> 角色管理中，为相应角色分配以下权限：
- `wk:taskAssign:list` - 查看工单分配列表
- `wk:taskAssign:query` - 查询工单分配详情
- `wk:taskAssign:assignSingle` - 分配给单个用户
- `wk:taskAssign:assignTwo` - 分配给两个用户

## 使用流程

1. **访问页面**：进入工单自动分配页面（实效管理 -> 工单管理 -> 工单自动分配）
2. **筛选项目**：使用筛选条件找到目标项目
3. **查看未分配工单**：在列表中查看每个项目的未分配工单数量
4. **分配工单**：
   - 点击"分配实施人员"按钮
   - 选择分配方式（单个或两个实施人员）
   - 选择实施人员
   - 确认分配

## 页面功能说明

### 筛选条件
- **项目名称**：支持模糊搜索
- **项目类型**：默认选择"人查"
- **状态**：默认选择"正常"
- **项目开始时间**：筛选开始时间 >= 选择时间的项目
- **项目结束时间**：筛选结束时间 <= 选择时间的项目

### 项目列表
- **项目基本信息**：ID、名称、类型、状态、开始时间、结束时间
- **未分配工单数**：实时显示，用不同颜色标识（有未分配工单显示橙色，无未分配工单显示绿色）
- **操作按钮**：分配实施人员

### 分配对话框
- **分配方式选择**：单选框选择分配模式
- **实施人员选择**：用户选择器，支持搜索
- **已分配项目提示**：显示选择的实施人员已分配的其他项目
- **分配说明**：动态显示当前分配方式的说明

## 注意事项

1. **数据依赖**：功能依赖于现有的工单表（wk_task）和项目表（wk_project）
2. **权限控制**：需要配置相应的菜单和按钮权限
3. **菜单位置**：菜单放在实效管理 -> 工单管理下面，与工单管理同级
4. **未分配工单识别**：通过 `attach_status = 0` 或 `principal_id IS NULL` 识别未分配工单
5. **分配结果**：分配成功后会显示具体的分配数量和结果

## 扩展说明

这个功能是一个简化版本，专注于核心的工单自动分配功能。如果后续需要更复杂的功能（如实施人员管理、分配历史记录等），可以基于现有代码进行扩展。
