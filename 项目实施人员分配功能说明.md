# 项目实施人员分配功能说明

## 功能概述

本功能实现了项目实施人员的分配管理，支持为每个项目分配最多两个实施人员，并可以设定执行顺序（正序/逆序），同时支持自动分配工单给实施人员。

## 主要特性

1. **项目筛选**：支持按项目名称、项目类型（人查）、状态（正常）进行筛选
2. **实施人员分配**：每个项目最多可分配两个实施人员，分别设定正序和逆序执行
3. **工单自动分配**：根据实施人员配置自动分配剩余工单
4. **已分配项目提示**：显示实施人员已分配的其他项目信息

## 数据库设计

### 新增表：wk_project_implementer

```sql
CREATE TABLE IF NOT EXISTS `wk_project_implementer` (
  `implementer_id` bigint(20) NOT NULL auto_increment COMMENT '分配记录ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `user_id` bigint(20) NOT NULL COMMENT '实施人员用户ID',
  `execution_order` int(1) DEFAULT 1 COMMENT '执行顺序：1-正序，2-逆序',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `status` int(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`implementer_id`),
  UNIQUE KEY `uk_project_user_order` (`project_id`, `user_id`, `execution_order`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='项目实施人员分配表';
```

## 后端API接口

### 主要接口

1. **GET /wk/implementer/listByProject/{projectId}** - 查询项目的实施人员
2. **POST /wk/implementer/assign** - 分配实施人员到项目
3. **POST /wk/implementer/autoAssignTasks/{projectId}** - 自动分配工单
4. **GET /wk/implementer/listByUser/{userId}** - 查询用户已分配的项目

### 分配请求参数

```json
{
  "projectId": 1,
  "forwardUserId": 2,
  "reverseUserId": 3,
  "autoAssignTasks": true
}
```

## 前端页面

### 页面路径
- 主页面：`/views/wk/implementer/index.vue`
- API封装：`/api/wk/implementer.js`

### 主要功能

1. **项目列表展示**
   - 显示项目基本信息
   - 显示已分配的实施人员（正序/逆序标识）
   - 支持按项目名称、类型、状态筛选

2. **实施人员分配对话框**
   - 选择正序实施人员
   - 选择逆序实施人员
   - 显示用户已分配的其他项目提示
   - 支持立即自动分配工单选项

3. **自动分配工单**
   - 一键分配项目下的未分配工单
   - 根据实施人员数量智能分配

## 业务逻辑

### 工单分配规则

1. **单个实施人员**：所有未分配工单全部分配给该实施人员
2. **两个实施人员**：未分配工单平均分配
   - 正序实施人员：分配前一半工单
   - 逆序实施人员：分配后一半工单

### 验证规则

1. 每个项目最多只能分配两个实施人员
2. 正序和逆序不能分配给同一个用户
3. 至少需要分配一个实施人员

## 使用流程

1. **访问页面**：进入项目实施人员分配页面
2. **筛选项目**：使用筛选条件找到目标项目（项目类型=人查，状态=正常）
3. **分配实施人员**：
   - 点击"分配实施人员"按钮
   - 选择正序和/或逆序实施人员
   - 可选择是否立即自动分配工单
   - 确认分配
4. **自动分配工单**：如果未在分配时选择自动分配，可后续点击"自动分配工单"按钮

## 权限配置

需要配置以下权限：
- `wk:implementer:list` - 查看实施人员分配列表
- `wk:implementer:assign` - 分配实施人员
- `wk:implementer:autoAssign` - 自动分配工单
- `wk:implementer:query` - 查询实施人员分配详情
- `wk:implementer:add` - 新增实施人员分配
- `wk:implementer:edit` - 编辑实施人员分配
- `wk:implementer:remove` - 删除实施人员分配
- `wk:implementer:export` - 导出实施人员分配数据

## 部署步骤

### 1. 数据库部署
```sql
-- 执行数据库表创建脚本
source sql/worker.sql;

-- 配置菜单（需要先查询实际的菜单结构）
-- 查询实效管理和工单管理的菜单ID
SELECT menu_id, menu_name, parent_id FROM sys_menu WHERE menu_name IN ('实效管理', '工单管理') ORDER BY parent_id;

-- 根据查询结果修改 sql/implementer_menu.sql 中的 parent_id，然后执行
source sql/implementer_menu.sql;
```

### 2. 后端部署
确保以下文件已正确部署：
- `worker/src/main/java/com/xz/worker/domain/WkProjectImplementer.java`
- `worker/src/main/java/com/xz/worker/mapper/WkProjectImplementerMapper.java`
- `worker/src/main/resources/mapper/wk/WkProjectImplementerMapper.xml`
- `worker/src/main/java/com/xz/worker/service/IWkProjectImplementerService.java`
- `worker/src/main/java/com/xz/worker/service/impl/WkProjectImplementerServiceImpl.java`
- `worker/src/main/java/com/xz/worker/domain/dto/ProjectImplementerAssignDto.java`
- `worker/src/main/java/com/xz/worker/controller/WkProjectImplementerController.java`

### 3. 前端部署
确保以下文件已正确部署：
- `ruoyi-ui/src/api/wk/implementer.js`
- `ruoyi-ui/src/views/wk/implementer/index.vue`

### 4. 权限配置
在系统管理 -> 角色管理中，为相应角色分配以下权限：
- `wk:implementer:list` - 查看项目实施人员分配列表
- `wk:implementer:assign` - 分配实施人员
- `wk:implementer:autoAssign` - 自动分配工单
- `wk:implementer:query` - 查询详情
- `wk:implementer:add` - 新增
- `wk:implementer:edit` - 编辑
- `wk:implementer:remove` - 删除
- `wk:implementer:export` - 导出

### 5. 菜单配置说明
菜单位置：实效管理 -> 工单管理 -> 项目实施人员分配

如果菜单没有正确显示，请：
1. 检查菜单配置是否正确执行
2. 确认用户角色是否有相应权限
3. 重新登录系统或清除浏览器缓存

## 功能更新说明

### 新增功能
1. **时间筛选**：支持按项目开始时间和结束时间筛选
2. **菜单位置**：功能菜单放置在实效管理 -> 工单管理下面

### 页面字段说明
- **项目开始时间**：对应数据库字段 `startDate`
- **项目结束时间**：对应数据库字段 `endDate`
- **筛选逻辑**：开始时间 >= 选择的开始时间，结束时间 <= 选择的结束时间

## 注意事项

1. 分配实施人员时会先删除项目原有的分配记录，然后创建新的分配记录
2. 自动分配工单只会分配状态为"未分配"的工单（principal_id为空）
3. 实施人员可以被分配到多个项目，系统会显示已分配项目的提示信息
4. 删除操作为逻辑删除，不会物理删除数据
5. 菜单配置中的parent_id需要根据实际的菜单结构进行调整
6. 时间筛选支持单独使用开始时间或结束时间，也可以同时使用
