# 项目实施人员分配功能测试验证步骤

## 前置条件
1. 系统中已有项目数据（项目类型为"人查"，状态为"正常"）
2. 系统中已有用户数据（实施人员）
3. 项目下有未分配的工单数据

## 测试步骤

### 1. 菜单访问测试
- [ ] 登录系统
- [ ] 导航到：实效管理 -> 工单管理 -> 项目实施人员分配
- [ ] 验证页面能正常加载

### 2. 项目列表功能测试
- [ ] 验证项目列表正常显示
- [ ] 验证默认筛选条件（项目类型=人查，状态=正常）
- [ ] 测试项目名称筛选功能
- [ ] 测试项目类型筛选功能
- [ ] 测试状态筛选功能

### 3. 时间筛选功能测试
- [ ] 测试项目开始时间筛选
  - 选择一个开始时间，验证只显示开始时间 >= 选择时间的项目
- [ ] 测试项目结束时间筛选
  - 选择一个结束时间，验证只显示结束时间 <= 选择时间的项目
- [ ] 测试同时使用开始时间和结束时间筛选
- [ ] 测试清空时间筛选条件

### 4. 实施人员分配功能测试

#### 4.1 分配单个实施人员
- [ ] 选择一个项目，点击"分配实施人员"
- [ ] 只选择正序实施人员，点击确定
- [ ] 验证分配成功，项目列表中显示实施人员信息
- [ ] 验证实施人员标签显示"正序"

#### 4.2 分配两个实施人员
- [ ] 选择一个项目，点击"分配实施人员"
- [ ] 选择正序和逆序实施人员（不同用户），点击确定
- [ ] 验证分配成功，项目列表中显示两个实施人员
- [ ] 验证实施人员标签分别显示"正序"和"逆序"

#### 4.3 已分配项目提示测试
- [ ] 选择一个已分配给其他项目的用户
- [ ] 验证显示"已分配项目"提示信息
- [ ] 验证提示信息中包含正确的项目名称

#### 4.4 分配验证测试
- [ ] 尝试分配相同用户到正序和逆序，验证报错提示
- [ ] 尝试不选择任何实施人员，验证报错提示

### 5. 自动分配工单功能测试

#### 5.1 单个实施人员工单分配
- [ ] 为项目分配一个实施人员
- [ ] 确保项目下有未分配的工单
- [ ] 点击"自动分配工单"
- [ ] 验证所有未分配工单都分配给了该实施人员

#### 5.2 两个实施人员工单分配
- [ ] 为项目分配两个实施人员（正序+逆序）
- [ ] 确保项目下有未分配的工单（建议10个以上）
- [ ] 点击"自动分配工单"
- [ ] 验证工单平均分配给两个实施人员
- [ ] 验证正序实施人员分配到前一半工单
- [ ] 验证逆序实施人员分配到后一半工单

#### 5.3 立即分配工单测试
- [ ] 在分配实施人员时勾选"自动分配工单"
- [ ] 验证分配完成后工单自动分配

### 6. 数据一致性测试
- [ ] 重新分配实施人员，验证原有分配记录被正确删除
- [ ] 验证数据库中的分配记录状态正确
- [ ] 验证工单的principal_id字段正确更新

### 7. 权限测试
- [ ] 使用没有相应权限的用户登录
- [ ] 验证菜单不显示或功能按钮不可用
- [ ] 验证API接口返回权限错误

### 8. 异常情况测试
- [ ] 测试分配不存在的用户
- [ ] 测试分配到不存在的项目
- [ ] 测试网络异常情况下的错误处理
- [ ] 测试并发分配同一项目的情况

## 预期结果

### 正常功能
1. 所有筛选条件正常工作
2. 实施人员分配成功，显示正确
3. 工单自动分配按规则执行
4. 已分配项目提示正确显示
5. 数据一致性保持良好

### 错误处理
1. 输入验证错误有友好提示
2. 权限不足有明确提示
3. 网络异常有重试机制
4. 并发操作有冲突检测

## 测试数据准备

### 项目数据
```sql
-- 确保有测试项目
SELECT project_id, name, type, status, startDate, endDate 
FROM wk_project 
WHERE type = 1 AND status = 0 
LIMIT 5;
```

### 用户数据
```sql
-- 确保有测试用户
SELECT user_id, nick_name, status 
FROM sys_user 
WHERE status = '0' AND del_flag = '0' 
LIMIT 10;
```

### 工单数据
```sql
-- 确保有未分配的工单
SELECT task_id, project_id, principal_id, status 
FROM wk_task 
WHERE principal_id IS NULL 
LIMIT 20;
```

## 问题记录
在测试过程中发现的问题请记录在此：

| 问题描述 | 重现步骤 | 预期结果 | 实际结果 | 状态 |
|---------|---------|---------|---------|------|
|         |         |         |         |      |

## 测试完成确认
- [ ] 所有功能测试通过
- [ ] 所有异常测试通过
- [ ] 性能测试通过
- [ ] 用户体验良好
- [ ] 文档完整准确
